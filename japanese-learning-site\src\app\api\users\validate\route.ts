import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/lib/auth'
import fs from 'fs'
import path from 'path'

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value
    
    if (!token) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }

    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: '无效token' }, { status: 401 })
    }

    // 读取用户数据
    const usersPath = path.join(process.cwd(), 'data', 'users.json')
    const usersData = fs.readFileSync(usersPath, 'utf8')
    const users = JSON.parse(usersData)

    // 获取所有有效的学生ID
    const validStudentIds = Object.values(users)
      .filter((user: any) => user.role === 'student')
      .map((user: any) => user.id)

    return NextResponse.json({ validStudentIds })
  } catch (error) {
    console.error('验证用户失败:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
