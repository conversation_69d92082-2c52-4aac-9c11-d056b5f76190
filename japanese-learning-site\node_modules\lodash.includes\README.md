# lodash.includes v4.3.0

The [lodash](https://lodash.com/) method `_.includes` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.includes
```

In Node.js:
```js
var includes = require('lodash.includes');
```

See the [documentation](https://lodash.com/docs#includes) or [package source](https://github.com/lodash/lodash/blob/4.3.0-npm-packages/lodash.includes) for more details.
