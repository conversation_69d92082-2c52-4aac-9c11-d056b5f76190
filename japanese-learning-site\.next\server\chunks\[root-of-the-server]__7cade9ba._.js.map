{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken'\nimport bcrypt from 'bcryptjs'\nimport fs from 'fs'\nimport path from 'path'\n\nexport interface User {\n  id: string\n  username: string\n  password: string\n  name: string\n  pages: string[]\n}\n\nexport interface UserData {\n  [key: string]: User\n}\n\n// 获取用户数据\nexport function getUserData(): UserData {\n  const usersPath = path.join(process.cwd(), 'data', 'users.json')\n  const usersData = fs.readFileSync(usersPath, 'utf8')\n  return JSON.parse(usersData)\n}\n\n// 验证用户凭据\nexport async function validateUser(username: string, password: string): Promise<User | null> {\n  const users = getUserData()\n  const user = Object.values(users).find(u => u.username === username)\n\n  if (!user) {\n    return null\n  }\n\n  const isValid = await bcrypt.compare(password, user.password)\n  return isValid ? user : null\n}\n\n// 生成JWT token\nexport function generateToken(userId: string): string {\n  return jwt.sign({ userId }, process.env.JWT_SECRET!, { expiresIn: '7d' })\n}\n\n// 验证JWT token\nexport function verifyToken(token: string): { userId: string } | null {\n  try {\n    return jwt.verify(token, process.env.JWT_SECRET!) as { userId: string }\n  } catch {\n    return null\n  }\n}\n\n// 获取用户页面配置\nexport function getUserPages(userId: string) {\n  try {\n    const pagesPath = path.join(process.cwd(), 'data', 'content', userId, 'pages.json')\n    const pagesData = fs.readFileSync(pagesPath, 'utf8')\n    return JSON.parse(pagesData)\n  } catch {\n    return {}\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;AAeO,SAAS;IACd,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;IACnD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;IAC7C,OAAO,KAAK,KAAK,CAAC;AACpB;AAGO,eAAe,aAAa,QAAgB,EAAE,QAAgB;IACnE,MAAM,QAAQ;IACd,MAAM,OAAO,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IAE3D,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,UAAU,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ;IAC5D,OAAO,UAAU,OAAO;AAC1B;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC;QAAE;IAAO,GAAG,QAAQ,GAAG,CAAC,UAAU,EAAG;QAAE,WAAW;IAAK;AACzE;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU;IACjD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,aAAa,MAAc;IACzC,IAAI;QACF,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,WAAW,QAAQ;QACtE,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;QAC7C,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO,CAAC;IACV;AACF", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/app/api/lessons/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { verifyToken } from '@/lib/auth'\nimport fs from 'fs'\nimport path from 'path'\n\n// 获取课程列表\nexport async function GET(request: NextRequest) {\n  try {\n    const token = request.cookies.get('auth-token')?.value\n    \n    if (!token) {\n      return NextResponse.json({ error: '未授权' }, { status: 401 })\n    }\n\n    const decoded = verifyToken(token)\n    if (!decoded) {\n      return NextResponse.json({ error: '无效token' }, { status: 401 })\n    }\n\n    const lessonsPath = path.join(process.cwd(), 'data', 'lessons.json')\n    \n    if (!fs.existsSync(lessonsPath)) {\n      return NextResponse.json({ lessons: [] })\n    }\n\n    const lessonsData = fs.readFileSync(lessonsPath, 'utf8')\n    const { lessons } = JSON.parse(lessonsData)\n\n    // 根据用户角色过滤课程\n    const userLessons = lessons.filter((lesson: any) => \n      lesson.students.includes(decoded.userId) || lesson.teacher === decoded.userId\n    )\n\n    return NextResponse.json({ lessons: userLessons })\n  } catch (error) {\n    console.error('获取课程列表错误:', error)\n    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })\n  }\n}\n\n// 发布新课程\nexport async function POST(request: NextRequest) {\n  try {\n    const token = request.cookies.get('auth-token')?.value\n    \n    if (!token) {\n      return NextResponse.json({ error: '未授权' }, { status: 401 })\n    }\n\n    const decoded = verifyToken(token)\n    if (!decoded) {\n      return NextResponse.json({ error: '无效token' }, { status: 401 })\n    }\n\n    // 验证是否为教师\n    const usersPath = path.join(process.cwd(), 'data', 'users.json')\n    const usersData = fs.readFileSync(usersPath, 'utf8')\n    const users = JSON.parse(usersData)\n    const user = users[decoded.userId]\n\n    if (!user || user.role !== 'teacher') {\n      return NextResponse.json({ error: '只有教师可以发布课程' }, { status: 403 })\n    }\n\n    const lessonData = await request.json()\n    \n    // 生成课程ID\n    const lessonId = `lesson_${Date.now()}`\n    const now = new Date().toISOString()\n\n    const newLesson = {\n      id: lessonId,\n      title: lessonData.title,\n      date: new Date().toISOString().split('T')[0],\n      teacher: decoded.userId,\n      students: lessonData.students,\n      content: lessonData.content,\n      homework: lessonData.homework || '',\n      notes: lessonData.notes || '',\n      createdAt: now,\n      updatedAt: now\n    }\n\n    // 读取现有课程数据\n    const lessonsPath = path.join(process.cwd(), 'data', 'lessons.json')\n    let lessonsFile = { lessons: [] }\n    \n    if (fs.existsSync(lessonsPath)) {\n      const lessonsData = fs.readFileSync(lessonsPath, 'utf8')\n      lessonsFile = JSON.parse(lessonsData)\n    }\n\n    // 添加新课程\n    lessonsFile.lessons.unshift(newLesson)\n\n    // 保存课程数据\n    fs.writeFileSync(lessonsPath, JSON.stringify(lessonsFile, null, 2))\n\n    // 为每个学生添加课程页面\n    for (const studentId of lessonData.students) {\n      const studentPagesPath = path.join(process.cwd(), 'data', 'content', studentId, 'pages.json')\n      \n      if (fs.existsSync(studentPagesPath)) {\n        const pagesData = fs.readFileSync(studentPagesPath, 'utf8')\n        const pages = JSON.parse(pagesData)\n        \n        // 添加今日课程页面\n        pages[`lesson-${lessonId}`] = {\n          title: \"今日の授業\",\n          description: lessonData.title,\n          icon: \"today\",\n          type: \"lesson\",\n          lessonId: lessonId\n        }\n\n        fs.writeFileSync(studentPagesPath, JSON.stringify(pages, null, 2))\n      }\n    }\n\n    return NextResponse.json({ \n      success: true, \n      lesson: newLesson,\n      message: '课程发布成功！'\n    })\n  } catch (error) {\n    console.error('发布课程错误:', error)\n    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAM,GAAG;gBAAE,QAAQ;YAAI;QAC3D;QAEA,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAU,GAAG;gBAAE,QAAQ;YAAI;QAC/D;QAEA,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;QAErD,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,cAAc;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,SAAS,EAAE;YAAC;QACzC;QAEA,MAAM,cAAc,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,aAAa;QACjD,MAAM,EAAE,OAAO,EAAE,GAAG,KAAK,KAAK,CAAC;QAE/B,aAAa;QACb,MAAM,cAAc,QAAQ,MAAM,CAAC,CAAC,SAClC,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,MAAM,KAAK,OAAO,OAAO,KAAK,QAAQ,MAAM;QAG/E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAY;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAU,GAAG;YAAE,QAAQ;QAAI;IAC/D;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAM,GAAG;gBAAE,QAAQ;YAAI;QAC3D;QAEA,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAU,GAAG;gBAAE,QAAQ;YAAI;QAC/D;QAEA,UAAU;QACV,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;QACnD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;QAC7C,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,MAAM,OAAO,KAAK,CAAC,QAAQ,MAAM,CAAC;QAElC,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,WAAW;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAa,GAAG;gBAAE,QAAQ;YAAI;QAClE;QAEA,MAAM,aAAa,MAAM,QAAQ,IAAI;QAErC,SAAS;QACT,MAAM,WAAW,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;QACvC,MAAM,MAAM,IAAI,OAAO,WAAW;QAElC,MAAM,YAAY;YAChB,IAAI;YACJ,OAAO,WAAW,KAAK;YACvB,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC5C,SAAS,QAAQ,MAAM;YACvB,UAAU,WAAW,QAAQ;YAC7B,SAAS,WAAW,OAAO;YAC3B,UAAU,WAAW,QAAQ,IAAI;YACjC,OAAO,WAAW,KAAK,IAAI;YAC3B,WAAW;YACX,WAAW;QACb;QAEA,WAAW;QACX,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;QACrD,IAAI,cAAc;YAAE,SAAS,EAAE;QAAC;QAEhC,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,cAAc;YAC9B,MAAM,cAAc,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,aAAa;YACjD,cAAc,KAAK,KAAK,CAAC;QAC3B;QAEA,QAAQ;QACR,YAAY,OAAO,CAAC,OAAO,CAAC;QAE5B,SAAS;QACT,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,aAAa,KAAK,SAAS,CAAC,aAAa,MAAM;QAEhE,cAAc;QACd,KAAK,MAAM,aAAa,WAAW,QAAQ,CAAE;YAC3C,MAAM,mBAAmB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,WAAW,WAAW;YAEhF,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,mBAAmB;gBACnC,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,kBAAkB;gBACpD,MAAM,QAAQ,KAAK,KAAK,CAAC;gBAEzB,WAAW;gBACX,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG;oBAC5B,OAAO;oBACP,aAAa,WAAW,KAAK;oBAC7B,MAAM;oBACN,MAAM;oBACN,UAAU;gBACZ;gBAEA,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;YACjE;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,QAAQ;YACR,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAU,GAAG;YAAE,QAAQ;QAAI;IAC/D;AACF", "debugId": null}}]}