export default function NewLesson() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-6 flex items-center">
          <span className="material-icons-outlined text-blue-600 mr-3">auto_stories</span>
          新课程内容
        </h1>
        
        <div className="space-y-6">
          {/* 课程介绍 */}
          <section className="bg-blue-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-gray-900 mb-3 flex items-center">
              <span className="material-icons-outlined text-blue-600 mr-2">info</span>
              课程介绍
            </h2>
            <p className="text-gray-700">
              这里是新课程的介绍内容。您可以添加任何学习材料，包括文本、表格、练习等。
            </p>
          </section>

          {/* 学习要点 */}
          <section className="bg-green-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-gray-900 mb-3 flex items-center">
              <span className="material-icons-outlined text-green-600 mr-2">lightbulb</span>
              学习要点
            </h2>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-start">
                <span className="material-icons-outlined text-green-600 mr-2 mt-0.5 text-sm">check_circle</span>
                要点一：基础语法规则
              </li>
              <li className="flex items-start">
                <span className="material-icons-outlined text-green-600 mr-2 mt-0.5 text-sm">check_circle</span>
                要点二：常用词汇
              </li>
              <li className="flex items-start">
                <span className="material-icons-outlined text-green-600 mr-2 mt-0.5 text-sm">check_circle</span>
                要点三：实际应用
              </li>
            </ul>
          </section>

          {/* 练习题 */}
          <section className="bg-yellow-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-gray-900 mb-3 flex items-center">
              <span className="material-icons-outlined text-yellow-600 mr-2">quiz</span>
              练习题
            </h2>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded border">
                <p className="font-medium text-gray-900 mb-2">1. 请翻译以下句子：</p>
                <p className="text-gray-700 mb-2">こんにちは、元気ですか？</p>
                <div className="text-sm text-gray-600">
                  <p>答案：你好，你好吗？</p>
                </div>
              </div>
              
              <div className="bg-white p-4 rounded border">
                <p className="font-medium text-gray-900 mb-2">2. 选择正确的读音：</p>
                <p className="text-gray-700 mb-2">学校</p>
                <div className="space-y-1 text-sm">
                  <label className="flex items-center">
                    <input type="radio" name="q2" className="mr-2" />
                    がっこう (正确)
                  </label>
                  <label className="flex items-center">
                    <input type="radio" name="q2" className="mr-2" />
                    がくこう
                  </label>
                </div>
              </div>
            </div>
          </section>

          {/* 补充资料 */}
          <section className="bg-purple-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-gray-900 mb-3 flex items-center">
              <span className="material-icons-outlined text-purple-600 mr-2">library_books</span>
              补充资料
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white p-4 rounded border">
                <h3 className="font-medium text-gray-900 mb-2">相关链接</h3>
                <ul className="text-sm text-blue-600 space-y-1">
                  <li><a href="#" className="hover:underline">日语学习网站</a></li>
                  <li><a href="#" className="hover:underline">在线词典</a></li>
                </ul>
              </div>
              <div className="bg-white p-4 rounded border">
                <h3 className="font-medium text-gray-900 mb-2">推荐书籍</h3>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• 《标准日本语》</li>
                  <li>• 《大家的日语》</li>
                </ul>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  )
}
