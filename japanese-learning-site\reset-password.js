const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

// 生成新的密码哈希
const newPassword = 'password';
const newHash = bcrypt.hashSync(newPassword, 10);

console.log('New password:', newPassword);
console.log('New hash:', newHash);

// 读取用户数据
const usersPath = path.join(__dirname, 'data', 'users.json');
const usersData = fs.readFileSync(usersPath, 'utf8');
const users = JSON.parse(usersData);

// 更新所有用户的密码
users.student1.password = newHash;
users.student2.password = newHash;

// 保存更新后的数据
fs.writeFileSync(usersPath, JSON.stringify(users, null, 2));

console.log('Password updated for all users!');
console.log('Test login with:');
console.log('Username: student1 or student2');
console.log('Password: password');
