{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken'\nimport bcrypt from 'bcryptjs'\nimport fs from 'fs'\nimport path from 'path'\n\nexport interface User {\n  id: string\n  username: string\n  password: string\n  name: string\n  pages: string[]\n}\n\nexport interface UserData {\n  [key: string]: User\n}\n\n// 获取用户数据\nexport function getUserData(): UserData {\n  const usersPath = path.join(process.cwd(), 'data', 'users.json')\n  const usersData = fs.readFileSync(usersPath, 'utf8')\n  return JSON.parse(usersData)\n}\n\n// 验证用户凭据\nexport async function validateUser(username: string, password: string): Promise<User | null> {\n  const users = getUserData()\n  const user = Object.values(users).find(u => u.username === username)\n\n  if (!user) {\n    return null\n  }\n\n  const isValid = await bcrypt.compare(password, user.password)\n  return isValid ? user : null\n}\n\n// 生成JWT token\nexport function generateToken(userId: string): string {\n  return jwt.sign({ userId }, process.env.JWT_SECRET!, { expiresIn: '7d' })\n}\n\n// 验证JWT token\nexport function verifyToken(token: string): { userId: string } | null {\n  try {\n    return jwt.verify(token, process.env.JWT_SECRET!) as { userId: string }\n  } catch {\n    return null\n  }\n}\n\n// 获取用户页面配置\nexport function getUserPages(userId: string) {\n  try {\n    const pagesPath = path.join(process.cwd(), 'data', 'content', userId, 'pages.json')\n    const pagesData = fs.readFileSync(pagesPath, 'utf8')\n    return JSON.parse(pagesData)\n  } catch {\n    return {}\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;AAeO,SAAS;IACd,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;IACnD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;IAC7C,OAAO,KAAK,KAAK,CAAC;AACpB;AAGO,eAAe,aAAa,QAAgB,EAAE,QAAgB;IACnE,MAAM,QAAQ;IACd,MAAM,OAAO,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IAE3D,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,UAAU,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ;IAC5D,OAAO,UAAU,OAAO;AAC1B;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC;QAAE;IAAO,GAAG,QAAQ,GAAG,CAAC,UAAU,EAAG;QAAE,WAAW;IAAK;AACzE;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU;IACjD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,aAAa,MAAc;IACzC,IAAI;QACF,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,WAAW,QAAQ;QACtE,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;QAC7C,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO,CAAC;IACV;AACF", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { validateUser, generateToken } from '@/lib/auth'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { username, password } = await request.json()\n    console.log('Login attempt:', { username, passwordLength: password?.length })\n\n    if (!username || !password) {\n      console.log('Missing credentials')\n      return NextResponse.json(\n        { error: '用户名和密码不能为空' },\n        { status: 400 }\n      )\n    }\n\n    const user = await validateUser(username, password)\n    console.log('User validation result:', user ? 'success' : 'failed')\n\n    if (!user) {\n      console.log('Invalid credentials for user:', username)\n      return NextResponse.json(\n        { error: '用户名或密码错误' },\n        { status: 401 }\n      )\n    }\n\n    const token = generateToken(user.id)\n    \n    const response = NextResponse.json({\n      success: true,\n      user: {\n        id: user.id,\n        username: user.username,\n        name: user.name\n      }\n    })\n\n    // 设置httpOnly cookie\n    response.cookies.set('auth-token', token, {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === 'production',\n      sameSite: 'strict',\n      maxAge: 7 * 24 * 60 * 60 // 7天\n    })\n\n    return response\n  } catch (error) {\n    console.error('登录错误:', error)\n    return NextResponse.json(\n      { error: '服务器内部错误' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QACjD,QAAQ,GAAG,CAAC,kBAAkB;YAAE;YAAU,gBAAgB,UAAU;QAAO;QAE3E,IAAI,CAAC,YAAY,CAAC,UAAU;YAC1B,QAAQ,GAAG,CAAC;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAa,GACtB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE,UAAU;QAC1C,QAAQ,GAAG,CAAC,2BAA2B,OAAO,YAAY;QAE1D,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,iCAAiC;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAW,GACpB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,EAAE;QAEnC,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACjC,SAAS;YACT,MAAM;gBACJ,IAAI,KAAK,EAAE;gBACX,UAAU,KAAK,QAAQ;gBACvB,MAAM,KAAK,IAAI;YACjB;QACF;QAEA,oBAAoB;QACpB,SAAS,OAAO,CAAC,GAAG,CAAC,cAAc,OAAO;YACxC,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ,IAAI,KAAK,KAAK,GAAG,KAAK;QAChC;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,SAAS;QACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAU,GACnB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}