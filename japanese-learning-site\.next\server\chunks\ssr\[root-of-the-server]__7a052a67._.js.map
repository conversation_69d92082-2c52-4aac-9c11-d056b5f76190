{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken'\nimport bcrypt from 'bcryptjs'\nimport fs from 'fs'\nimport path from 'path'\n\nexport interface User {\n  id: string\n  username: string\n  password: string\n  name: string\n  pages: string[]\n}\n\nexport interface UserData {\n  [key: string]: User\n}\n\n// 获取用户数据\nexport function getUserData(): UserData {\n  const usersPath = path.join(process.cwd(), 'data', 'users.json')\n  const usersData = fs.readFileSync(usersPath, 'utf8')\n  return JSON.parse(usersData)\n}\n\n// 验证用户凭据\nexport async function validateUser(username: string, password: string): Promise<User | null> {\n  const users = getUserData()\n  const user = Object.values(users).find(u => u.username === username)\n\n  if (!user) {\n    return null\n  }\n\n  const isValid = await bcrypt.compare(password, user.password)\n  return isValid ? user : null\n}\n\n// 生成JWT token\nexport function generateToken(userId: string): string {\n  return jwt.sign({ userId }, process.env.JWT_SECRET!, { expiresIn: '7d' })\n}\n\n// 验证JWT token\nexport function verifyToken(token: string): { userId: string } | null {\n  try {\n    return jwt.verify(token, process.env.JWT_SECRET!) as { userId: string }\n  } catch {\n    return null\n  }\n}\n\n// 获取用户页面配置\nexport function getUserPages(userId: string) {\n  try {\n    const pagesPath = path.join(process.cwd(), 'data', 'content', userId, 'pages.json')\n    const pagesData = fs.readFileSync(pagesPath, 'utf8')\n    return JSON.parse(pagesData)\n  } catch {\n    return {}\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;AAeO,SAAS;IACd,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;IACnD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;IAC7C,OAAO,KAAK,KAAK,CAAC;AACpB;AAGO,eAAe,aAAa,QAAgB,EAAE,QAAgB;IACnE,MAAM,QAAQ;IACd,MAAM,OAAO,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IAE3D,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,UAAU,MAAM,iIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ;IAC5D,OAAO,UAAU,OAAO;AAC1B;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,qIAAA,CAAA,UAAG,CAAC,IAAI,CAAC;QAAE;IAAO,GAAG,QAAQ,GAAG,CAAC,UAAU,EAAG;QAAE,WAAW;IAAK;AACzE;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,qIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU;IACjD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,aAAa,MAAc;IACzC,IAAI;QACF,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,WAAW,QAAQ;QACtE,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;QAC7C,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO,CAAC;IACV;AACF", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/Layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Layout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Layout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/Layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Layout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Layout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/StudyNotes.tsx"], "sourcesContent": ["export default function StudyNotes() {\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-6 flex items-center\">\n          <span className=\"material-icons-outlined text-blue-600 mr-3\">school</span>\n          日语学习回顾\n        </h1>\n        \n        <div className=\"bg-blue-50 border-l-4 border-blue-500 p-4 mb-8\">\n          <div className=\"flex items-center\">\n            <span className=\"material-icons-outlined text-blue-600 mr-2\">event_note</span>\n            <p className=\"text-gray-700\">\n              这是一份根据老师在 <strong>水曜日 7:40</strong> 发送的消息整理的学习笔记。\n            </p>\n          </div>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm\">\n            <thead>\n              <tr className=\"bg-gray-50\">\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b\">类别</th>\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b\">原文</th>\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b\">说明与注解</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-blue-500 mr-2\">volume_up</span>\n                    发音\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b font-medium text-blue-800\">簡単</td>\n                <td className=\"px-6 py-4 border-b text-gray-700\">发音重点练习词：kantan (简单)</td>\n              </tr>\n              <tr className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-blue-500 mr-2\">volume_up</span>\n                    发音\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b font-medium text-blue-800\">パートナー</td>\n                <td className=\"px-6 py-4 border-b text-gray-700\">发音重点练习词：pātonā (伙伴、搭档)</td>\n              </tr>\n              <tr className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-green-500 mr-2\">chat_bubble_outline</span>\n                    表现\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b font-medium text-blue-800\">仕入れ先</td>\n                <td className=\"px-6 py-4 border-b text-gray-700\">词汇学习：しいれさき (供应商)</td>\n              </tr>\n              <tr className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-green-500 mr-2\">chat_bubble_outline</span>\n                    表现\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-gray-400 line-through font-medium\">政権交代</span>\n                    <span className=\"text-red-500 font-bold\">→</span>\n                    <span className=\"bg-green-100 text-green-800 px-2 py-1 rounded font-medium\">世代交代</span>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b text-gray-700\">用法辨析：老师提示将\"政权更迭\"联想到\"世代交替\"。</td>\n              </tr>\n              <tr className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-green-500 mr-2\">chat_bubble_outline</span>\n                    表现\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b font-medium text-blue-800\">終身雇用</td>\n                <td className=\"px-6 py-4 border-b text-gray-700\">词汇学习：しゅうしんこよう (终身雇佣制)</td>\n              </tr>\n              <tr className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-green-500 mr-2\">chat_bubble_outline</span>\n                    表现\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"font-medium text-blue-800\">将来性</span>\n                    <span className=\"text-green-600 font-bold\">≒</span>\n                    <span className=\"font-medium text-blue-800\">伸びしろ</span>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b text-gray-700\">近义词辨析：\"未来潜力\" (しょうらいせい) 与 \"成长空间\" (のびしろ) 意思相近。</td>\n              </tr>\n              <tr className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-green-500 mr-2\">chat_bubble_outline</span>\n                    表现\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 font-medium text-blue-800\">地雷を踏む</td>\n                <td className=\"px-6 py-4 text-gray-700\">惯用语：じらいをふむ，字面意思是\"踩到地雷\"，引申为\"触及敏感或危险的话题\"。</td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n        \n        <div className=\"mt-8 text-center bg-yellow-50 p-4 rounded-lg\">\n          <div className=\"flex items-center justify-center\">\n            <span className=\"material-icons-outlined text-yellow-600 mr-2\">forward_to_inbox</span>\n            <p className=\"text-gray-700 italic\">\n              老师的结束语：\"では、また明日の授業楽しみにしております。\" (那么，期待明天的课。)\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAK,WAAU;sCAA6C;;;;;;wBAAa;;;;;;;8BAI5E,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA6C;;;;;;0CAC7D,8OAAC;gCAAE,WAAU;;oCAAgB;kDACjB,8OAAC;kDAAO;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAKzC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;0CACC,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;sDAAmE;;;;;;sDACjF,8OAAC;4CAAG,WAAU;sDAAmE;;;;;;sDACjF,8OAAC;4CAAG,WAAU;sDAAmE;;;;;;;;;;;;;;;;;0CAGrF,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA6C;;;;;;wDAAgB;;;;;;;;;;;;0DAIjF,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA6C;;;;;;wDAAgB;;;;;;;;;;;;0DAIjF,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA8C;;;;;;wDAA0B;;;;;;;;;;;;0DAI5F,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA8C;;;;;;wDAA0B;;;;;;;;;;;;0DAI5F,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAyC;;;;;;sEACzD,8OAAC;4DAAK,WAAU;sEAAyB;;;;;;sEACzC,8OAAC;4DAAK,WAAU;sEAA4D;;;;;;;;;;;;;;;;;0DAGhF,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA8C;;;;;;wDAA0B;;;;;;;;;;;;0DAI5F,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA8C;;;;;;wDAA0B;;;;;;;;;;;;0DAI5F,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,8OAAC;4DAAK,WAAU;sEAA2B;;;;;;sEAC3C,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;;;;;;0DAGhD,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA8C;;;;;;wDAA0B;;;;;;;;;;;;0DAI5F,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAG,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMhD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA+C;;;;;;0CAC/D,8OAAC;gCAAE,WAAU;0CAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/VocabularyTable.tsx"], "sourcesContent": ["export default function VocabularyTable() {\n  const vocabularyData = [\n    { japanese: \"エレベーター\", chinese: \"电梯\" },\n    { japanese: \"エスカレーター\", chinese: \"自动扶梯\" },\n    { japanese: \"つけっぱなし\", chinese: \"（电器等）一直开着，未关闭\" },\n    { japanese: \"できるだけ\", chinese: \"尽可能，尽量\" },\n    { japanese: \"一週間 (いっしゅうかん)\", chinese: \"一星期，一周\" },\n    { japanese: \"わたしたち\", chinese: \"我们\" },\n    { japanese: \"〜なかで\", chinese: \"在...之中\" },\n    { japanese: \"派遣会社 (はけんがいしゃ)\", chinese: \"人才派遣公司\" },\n    { japanese: \"メッセージ\", chinese: \"消息，信息，留言\" },\n    { japanese: \"恥ずかしがるので (はずかしがるので)\", chinese: \"因为（他/她）害羞\" },\n    { japanese: \"合併されちゃって (がっぺいされちゃって)\", chinese: \"（公司等）被合并了（口语形式）\" }\n  ]\n\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-6 flex items-center\">\n          <span className=\"material-icons-outlined text-blue-600 mr-3\">table_view</span>\n          <span className=\"text-blue-600\">日语词汇</span>\n          <span className=\"text-gray-600 ml-2\">对照表</span>\n        </h1>\n        \n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm\">\n            <thead>\n              <tr className=\"bg-gray-50\">\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-red-600 mr-2\">translate</span>\n                    日语\n                  </div>\n                </th>\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-green-600 mr-2\">g_translate</span>\n                    中文含义\n                  </div>\n                </th>\n              </tr>\n            </thead>\n            <tbody>\n              {vocabularyData.map((item, index) => (\n                <tr key={index} className=\"hover:bg-blue-50 transition-colors\">\n                  <td className=\"px-6 py-4 border-b font-medium text-blue-800 text-lg\">\n                    {item.japanese}\n                  </td>\n                  <td className=\"px-6 py-4 border-b text-gray-700\">\n                    {item.chinese}\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n        \n        <div className=\"mt-8 text-center text-sm text-gray-600 bg-gray-50 p-4 rounded-lg\">\n          <p>共收录 {vocabularyData.length} 个词汇</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,iBAAiB;QACrB;YAAE,UAAU;YAAU,SAAS;QAAK;QACpC;YAAE,UAAU;YAAW,SAAS;QAAO;QACvC;YAAE,UAAU;YAAU,SAAS;QAAgB;QAC/C;YAAE,UAAU;YAAS,SAAS;QAAS;QACvC;YAAE,UAAU;YAAiB,SAAS;QAAS;QAC/C;YAAE,UAAU;YAAS,SAAS;QAAK;QACnC;YAAE,UAAU;YAAQ,SAAS;QAAS;QACtC;YAAE,UAAU;YAAkB,SAAS;QAAS;QAChD;YAAE,UAAU;YAAS,SAAS;QAAW;QACzC;YAAE,UAAU;YAAuB,SAAS;QAAY;QACxD;YAAE,UAAU;YAAyB,SAAS;QAAkB;KACjE;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAK,WAAU;sCAA6C;;;;;;sCAC7D,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;sCAChC,8OAAC;4BAAK,WAAU;sCAAqB;;;;;;;;;;;;8BAGvC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;0CACC,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA4C;;;;;;oDAAgB;;;;;;;;;;;;sDAIhF,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA8C;;;;;;oDAAkB;;;;;;;;;;;;;;;;;;;;;;;0CAMxF,8OAAC;0CACE,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAG,WAAU;0DACX,KAAK,QAAQ;;;;;;0DAEhB,8OAAC;gDAAG,WAAU;0DACX,KAAK,OAAO;;;;;;;uCALR;;;;;;;;;;;;;;;;;;;;;8BAajB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;4BAAE;4BAAK,eAAe,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;;;;;AAKxC", "debugId": null}}, {"offset": {"line": 996, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/app/%5Bstudent%5D/%5Bpage%5D/page.tsx"], "sourcesContent": ["import { getUserPages } from '@/lib/auth'\nimport Layout from '@/components/Layout'\nimport StudyNotes from '@/components/StudyNotes'\nimport VocabularyTable from '@/components/VocabularyTable'\nimport NewLesson from '@/components/NewLesson'\nimport { notFound } from 'next/navigation'\n\ninterface PageProps {\n  params: Promise<{ student: string; page: string }>\n}\n\nexport default async function DynamicPage({ params }: PageProps) {\n  const { student, page } = await params\n  const pages = getUserPages(student)\n\n  // 检查页面是否存在\n  if (!pages[page]) {\n    notFound()\n  }\n\n  const pageInfo = pages[page]\n\n  // 根据页面类型渲染不同的内容\n  const renderContent = () => {\n    switch (page) {\n      case 'cet6':\n        return <StudyNotes />\n      case 'jp':\n        return <VocabularyTable />\n      case 'vocabulary':\n        return (\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"bg-white rounded-lg shadow-lg p-8\">\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-6 flex items-center\">\n                <span className=\"material-icons-outlined text-blue-600 mr-3\">quiz</span>\n                词汇练习\n              </h1>\n              <div className=\"text-center py-12\">\n                <span className=\"material-icons-outlined text-gray-400 text-6xl mb-4\">\n                  construction\n                </span>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  功能开发中\n                </h3>\n                <p className=\"text-gray-600\">\n                  词汇练习功能正在开发中，敬请期待！\n                </p>\n              </div>\n            </div>\n          </div>\n        )\n      case 'grammar':\n        return (\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"bg-white rounded-lg shadow-lg p-8\">\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-6 flex items-center\">\n                <span className=\"material-icons-outlined text-blue-600 mr-3\">auto_stories</span>\n                日语语法学习\n              </h1>\n              <div className=\"space-y-6\">\n                <div className=\"bg-blue-50 p-6 rounded-lg\">\n                  <h2 className=\"text-xl font-semibold text-gray-900 mb-3\">基础语法</h2>\n                  <p className=\"text-gray-700\">\n                    这里将包含日语基础语法规则和例句。\n                  </p>\n                </div>\n                <div className=\"bg-green-50 p-6 rounded-lg\">\n                  <h2 className=\"text-xl font-semibold text-gray-900 mb-3\">进阶语法</h2>\n                  <p className=\"text-gray-700\">\n                    这里将包含日语进阶语法内容。\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )\n      case 'reading':\n        return (\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"bg-white rounded-lg shadow-lg p-8\">\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-6 flex items-center\">\n                <span className=\"material-icons-outlined text-blue-600 mr-3\">menu_book</span>\n                阅读理解\n              </h1>\n              <div className=\"space-y-6\">\n                <div className=\"bg-yellow-50 p-6 rounded-lg\">\n                  <h2 className=\"text-xl font-semibold text-gray-900 mb-3\">阅读材料</h2>\n                  <p className=\"text-gray-700\">\n                    这里将包含各种日语阅读材料和练习。\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )\n      default:\n        return (\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"bg-white rounded-lg shadow-lg p-8\">\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">\n                {pageInfo.title}\n              </h1>\n              <p className=\"text-gray-600\">\n                {pageInfo.description}\n              </p>\n            </div>\n          </div>\n        )\n    }\n  }\n\n  return (\n    <Layout studentId={student} currentPage={page} pages={pages}>\n      {renderContent()}\n    </Layout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;AAAA;;;;;;;AAMe,eAAe,YAAY,EAAE,MAAM,EAAa;IAC7D,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,MAAM;IAChC,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD,EAAE;IAE3B,WAAW;IACX,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;QAChB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,WAAW,KAAK,CAAC,KAAK;IAE5B,gBAAgB;IAChB,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,gIAAA,CAAA,UAAU;;;;;YACpB,KAAK;gBACH,qBAAO,8OAAC,qIAAA,CAAA,UAAe;;;;;YACzB,KAAK;g<PERSON><PERSON>,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAA6C;;;;;;oCAAW;;;;;;;0CAG1E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAsD;;;;;;kDAGtE,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;YAOvC,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAA6C;;;;;;oCAAmB;;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAI/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzC,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAA6C;;;;;;oCAAgB;;;;;;;0CAG/E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzC;gBACE,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,SAAS,KAAK;;;;;;0CAEjB,8OAAC;gCAAE,WAAU;0CACV,SAAS,WAAW;;;;;;;;;;;;;;;;;QAKjC;IACF;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAM;QAAC,WAAW;QAAS,aAAa;QAAM,OAAO;kBACnD;;;;;;AAGP", "debugId": null}}]}