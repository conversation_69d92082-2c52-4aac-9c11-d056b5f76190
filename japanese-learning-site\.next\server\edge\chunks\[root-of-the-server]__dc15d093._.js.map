{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  // 允许访问登录页面、API路由和教师页面\n  if (pathname === '/login' || pathname.startsWith('/api/') || pathname === '/teacher') {\n    return NextResponse.next()\n  }\n\n  // 检查是否访问学生专属路径\n  const studentPathMatch = pathname.match(/^\\/([^\\/]+)/)\n  if (studentPathMatch) {\n    const requestedStudent = studentPathMatch[1]\n\n    // 获取JWT token\n    const token = request.cookies.get('auth-token')?.value\n\n    if (!token) {\n      // 未登录，重定向到登录页\n      console.log('No token found, redirecting to login')\n      return NextResponse.redirect(new URL('/login', request.url))\n    }\n\n    // 暂时简化验证 - 只要有token就允许访问\n    // 详细的权限验证在页面组件中进行\n    console.log('Token found, allowing access to:', pathname)\n    return NextResponse.next()\n  }\n\n  // 根路径重定向到登录页\n  if (pathname === '/') {\n    return NextResponse.redirect(new URL('/login', request.url))\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * 匹配所有路径除了:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,sBAAsB;IACtB,IAAI,aAAa,YAAY,SAAS,UAAU,CAAC,YAAY,aAAa,YAAY;QACpF,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,eAAe;IACf,MAAM,mBAAmB,SAAS,KAAK,CAAC;IACxC,IAAI,kBAAkB;QACpB,MAAM,mBAAmB,gBAAgB,CAAC,EAAE;QAE5C,cAAc;QACd,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,cAAc;YACd,QAAQ,GAAG,CAAC;YACZ,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC5D;QAEA,yBAAyB;QACzB,kBAAkB;QAClB,QAAQ,GAAG,CAAC,oCAAoC;QAChD,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,aAAa;IACb,IAAI,aAAa,KAAK;QACpB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;IAC5D;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}