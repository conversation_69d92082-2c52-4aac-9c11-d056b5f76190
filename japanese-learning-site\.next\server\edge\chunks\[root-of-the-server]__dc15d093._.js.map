{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  // 允许访问登录页面和API路由\n  if (pathname === '/login' || pathname.startsWith('/api/')) {\n    return NextResponse.next()\n  }\n\n  // 检查是否访问学生专属路径\n  const studentPathMatch = pathname.match(/^\\/([^\\/]+)/)\n  if (studentPathMatch) {\n    const requestedStudent = studentPathMatch[1]\n    \n    // 获取JWT token\n    const token = request.cookies.get('auth-token')?.value\n\n    if (!token) {\n      // 未登录，重定向到登录页\n      return NextResponse.redirect(new URL('/login', request.url))\n    }\n\n    try {\n      // 简化的token验证 - 在中间件中我们只检查token是否存在\n      // 详细验证在API路由中进行\n      const tokenParts = token.split('.')\n      if (tokenParts.length !== 3) {\n        throw new Error('Invalid token format')\n      }\n\n      // 解码payload (不验证签名，因为在Edge Runtime中有限制)\n      const payload = JSON.parse(atob(tokenParts[1]))\n\n      // 检查用户是否有权限访问该路径\n      if (payload.userId !== requestedStudent) {\n        // 无权限访问，返回404\n        return NextResponse.rewrite(new URL('/404', request.url))\n      }\n\n      return NextResponse.next()\n    } catch (error) {\n      // Token无效，重定向到登录页\n      return NextResponse.redirect(new URL('/login', request.url))\n    }\n  }\n\n  // 根路径重定向到登录页\n  if (pathname === '/') {\n    return NextResponse.redirect(new URL('/login', request.url))\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * 匹配所有路径除了:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,iBAAiB;IACjB,IAAI,aAAa,YAAY,SAAS,UAAU,CAAC,UAAU;QACzD,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,eAAe;IACf,MAAM,mBAAmB,SAAS,KAAK,CAAC;IACxC,IAAI,kBAAkB;QACpB,MAAM,mBAAmB,gBAAgB,CAAC,EAAE;QAE5C,cAAc;QACd,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,cAAc;YACd,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC5D;QAEA,IAAI;YACF,mCAAmC;YACnC,gBAAgB;YAChB,MAAM,aAAa,MAAM,KAAK,CAAC;YAC/B,IAAI,WAAW,MAAM,KAAK,GAAG;gBAC3B,MAAM,IAAI,MAAM;YAClB;YAEA,wCAAwC;YACxC,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,UAAU,CAAC,EAAE;YAE7C,iBAAiB;YACjB,IAAI,QAAQ,MAAM,KAAK,kBAAkB;gBACvC,cAAc;gBACd,OAAO,6LAAA,CAAA,eAAY,CAAC,OAAO,CAAC,IAAI,IAAI,QAAQ,QAAQ,GAAG;YACzD;YAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;QAC1B,EAAE,OAAO,OAAO;YACd,kBAAkB;YAClB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC5D;IACF;IAEA,aAAa;IACb,IAAI,aAAa,KAAK;QACpB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;IAC5D;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}