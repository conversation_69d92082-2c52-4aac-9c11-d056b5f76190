{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  // 允许访问登录页面、API路由和教师页面\n  if (pathname === '/login' || pathname.startsWith('/api/') || pathname === '/teacher') {\n    return NextResponse.next()\n  }\n\n  // 检查是否访问学生专属路径\n  const studentPathMatch = pathname.match(/^\\/([^\\/]+)/)\n  if (studentPathMatch) {\n    const requestedStudent = studentPathMatch[1]\n\n    // 获取JWT token\n    const token = request.cookies.get('auth-token')?.value\n\n    if (!token) {\n      // 未登录，重定向到登录页\n      return NextResponse.redirect(new URL('/login', request.url))\n    }\n\n    // 验证学生是否存在\n    try {\n      const usersResponse = await fetch(new URL('/api/users/validate', request.url), {\n        headers: {\n          'Cookie': request.headers.get('cookie') || ''\n        }\n      })\n\n      if (usersResponse.ok) {\n        const data = await usersResponse.json()\n        const validStudentIds = data.validStudentIds || []\n\n        // 检查请求的学生ID是否有效\n        if (!validStudentIds.includes(requestedStudent)) {\n          // 学生不存在，返回404\n          return NextResponse.rewrite(new URL('/404', request.url))\n        }\n      }\n    } catch (error) {\n      console.error('验证学生ID失败:', error)\n      // 验证失败时重定向到登录页\n      return NextResponse.redirect(new URL('/login', request.url))\n    }\n\n    return NextResponse.next()\n  }\n\n  // 根路径重定向到登录页\n  if (pathname === '/') {\n    return NextResponse.redirect(new URL('/login', request.url))\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * 匹配所有路径除了:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,sBAAsB;IACtB,IAAI,aAAa,YAAY,SAAS,UAAU,CAAC,YAAY,aAAa,YAAY;QACpF,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,eAAe;IACf,MAAM,mBAAmB,SAAS,KAAK,CAAC;IACxC,IAAI,kBAAkB;QACpB,MAAM,mBAAmB,gBAAgB,CAAC,EAAE;QAE5C,cAAc;QACd,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,cAAc;YACd,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC5D;QAEA,WAAW;QACX,IAAI;YACF,MAAM,gBAAgB,MAAM,MAAM,IAAI,IAAI,uBAAuB,QAAQ,GAAG,GAAG;gBAC7E,SAAS;oBACP,UAAU,QAAQ,OAAO,CAAC,GAAG,CAAC,aAAa;gBAC7C;YACF;YAEA,IAAI,cAAc,EAAE,EAAE;gBACpB,MAAM,OAAO,MAAM,cAAc,IAAI;gBACrC,MAAM,kBAAkB,KAAK,eAAe,IAAI,EAAE;gBAElD,gBAAgB;gBAChB,IAAI,CAAC,gBAAgB,QAAQ,CAAC,mBAAmB;oBAC/C,cAAc;oBACd,OAAO,6LAAA,CAAA,eAAY,CAAC,OAAO,CAAC,IAAI,IAAI,QAAQ,QAAQ,GAAG;gBACzD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,eAAe;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC5D;QAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,aAAa;IACb,IAAI,aAAa,KAAK;QACpB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;IAC5D;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}