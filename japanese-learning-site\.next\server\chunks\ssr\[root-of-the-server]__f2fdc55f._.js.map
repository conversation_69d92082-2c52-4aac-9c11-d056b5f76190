{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/TeacherDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\n\ninterface PronunciationItem {\n  word: string\n  reading: string\n  meaning: string\n}\n\ninterface ExpressionItem {\n  expression: string\n  reading: string\n  meaning: string\n  usage: string\n  example: string\n}\n\nexport default function TeacherDashboard() {\n  const [lessonTitle, setLessonTitle] = useState('')\n  const [selectedStudents, setSelectedStudents] = useState<string[]>([])\n  const [pronunciationItems, setPronunciationItems] = useState<PronunciationItem[]>([\n    { word: '', reading: '', meaning: '' }\n  ])\n  const [expressionItems, setExpressionItems] = useState<ExpressionItem[]>([\n    { expression: '', reading: '', meaning: '', usage: '', example: '' }\n  ])\n  const [homework, setHomework] = useState('')\n  const [notes, setNotes] = useState('')\n\n  const students = [\n    { id: 'student1', name: '学生一' },\n    { id: 'student2', name: '学生二' }\n  ]\n\n  const addPronunciationItem = () => {\n    setPronunciationItems([...pronunciationItems, { word: '', reading: '', meaning: '' }])\n  }\n\n  const addExpressionItem = () => {\n    setExpressionItems([...expressionItems, { expression: '', reading: '', meaning: '', usage: '', example: '' }])\n  }\n\n  const updatePronunciationItem = (index: number, field: keyof PronunciationItem, value: string) => {\n    const updated = [...pronunciationItems]\n    updated[index][field] = value\n    setPronunciationItems(updated)\n  }\n\n  const updateExpressionItem = (index: number, field: keyof ExpressionItem, value: string) => {\n    const updated = [...expressionItems]\n    updated[index][field] = value\n    setExpressionItems(updated)\n  }\n\n  const removePronunciationItem = (index: number) => {\n    setPronunciationItems(pronunciationItems.filter((_, i) => i !== index))\n  }\n\n  const removeExpressionItem = (index: number) => {\n    setExpressionItems(expressionItems.filter((_, i) => i !== index))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    const lessonData = {\n      title: lessonTitle,\n      students: selectedStudents,\n      content: {\n        pronunciation: {\n          title: \"今日の発音\",\n          items: pronunciationItems.filter(item => item.word.trim())\n        },\n        expressions: {\n          title: \"今日の表現\", \n          items: expressionItems.filter(item => item.expression.trim())\n        }\n      },\n      homework,\n      notes\n    }\n\n    try {\n      const response = await fetch('/api/lessons', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(lessonData)\n      })\n\n      if (response.ok) {\n        alert('课程发布成功！')\n        // 重置表单\n        setLessonTitle('')\n        setSelectedStudents([])\n        setPronunciationItems([{ word: '', reading: '', meaning: '' }])\n        setExpressionItems([{ expression: '', reading: '', meaning: '', usage: '', example: '' }])\n        setHomework('')\n        setNotes('')\n      } else {\n        alert('发布失败，请重试')\n      }\n    } catch (error) {\n      console.error('发布错误:', error)\n      alert('发布失败，请重试')\n    }\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto\">\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-8 flex items-center\">\n          <span className=\"material-icons-outlined text-blue-600 mr-3\">school</span>\n          教师控制台 - 发布新课程\n        </h1>\n\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {/* 基本信息 */}\n          <div className=\"bg-gray-50 p-6 rounded-lg\">\n            <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n              <span className=\"material-icons-outlined text-blue-600 mr-2\">info</span>\n              课程基本信息\n            </h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  课程标题\n                </label>\n                <input\n                  type=\"text\"\n                  value={lessonTitle}\n                  onChange={(e) => setLessonTitle(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"例：2024年1月15日 - 日常问候\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  发布给学生\n                </label>\n                <div className=\"space-y-2\">\n                  {students.map(student => (\n                    <label key={student.id} className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        checked={selectedStudents.includes(student.id)}\n                        onChange={(e) => {\n                          if (e.target.checked) {\n                            setSelectedStudents([...selectedStudents, student.id])\n                          } else {\n                            setSelectedStudents(selectedStudents.filter(id => id !== student.id))\n                          }\n                        }}\n                        className=\"mr-2\"\n                      />\n                      {student.name}\n                    </label>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* 发音练习 */}\n          <div className=\"bg-red-50 p-6 rounded-lg\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h2 className=\"text-xl font-semibold flex items-center\">\n                <span className=\"material-icons-outlined text-red-600 mr-2\">record_voice_over</span>\n                今日の発音\n              </h2>\n              <button\n                type=\"button\"\n                onClick={addPronunciationItem}\n                className=\"bg-red-600 text-white px-3 py-1 rounded-md hover:bg-red-700 flex items-center\"\n              >\n                <span className=\"material-icons-outlined mr-1 text-sm\">add</span>\n                添加\n              </button>\n            </div>\n            <div className=\"space-y-4\">\n              {pronunciationItems.map((item, index) => (\n                <div key={index} className=\"bg-white p-4 rounded border grid grid-cols-1 md:grid-cols-4 gap-3\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"日语单词\"\n                    value={item.word}\n                    onChange={(e) => updatePronunciationItem(index, 'word', e.target.value)}\n                    className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                  />\n                  <input\n                    type=\"text\"\n                    placeholder=\"读音 (romaji)\"\n                    value={item.reading}\n                    onChange={(e) => updatePronunciationItem(index, 'reading', e.target.value)}\n                    className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                  />\n                  <input\n                    type=\"text\"\n                    placeholder=\"中文意思\"\n                    value={item.meaning}\n                    onChange={(e) => updatePronunciationItem(index, 'meaning', e.target.value)}\n                    className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => removePronunciationItem(index)}\n                    className=\"bg-red-500 text-white px-3 py-2 rounded-md hover:bg-red-600\"\n                  >\n                    删除\n                  </button>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* 表达练习 */}\n          <div className=\"bg-green-50 p-6 rounded-lg\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h2 className=\"text-xl font-semibold flex items-center\">\n                <span className=\"material-icons-outlined text-green-600 mr-2\">chat_bubble</span>\n                今日の表現\n              </h2>\n              <button\n                type=\"button\"\n                onClick={addExpressionItem}\n                className=\"bg-green-600 text-white px-3 py-1 rounded-md hover:bg-green-700 flex items-center\"\n              >\n                <span className=\"material-icons-outlined mr-1 text-sm\">add</span>\n                添加\n              </button>\n            </div>\n            <div className=\"space-y-4\">\n              {expressionItems.map((item, index) => (\n                <div key={index} className=\"bg-white p-4 rounded border space-y-3\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n                    <input\n                      type=\"text\"\n                      placeholder=\"日语表达\"\n                      value={item.expression}\n                      onChange={(e) => updateExpressionItem(index, 'expression', e.target.value)}\n                      className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    />\n                    <input\n                      type=\"text\"\n                      placeholder=\"读音 (romaji)\"\n                      value={item.reading}\n                      onChange={(e) => updateExpressionItem(index, 'reading', e.target.value)}\n                      className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    />\n                    <input\n                      type=\"text\"\n                      placeholder=\"中文意思\"\n                      value={item.meaning}\n                      onChange={(e) => updateExpressionItem(index, 'meaning', e.target.value)}\n                      className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    />\n                  </div>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                    <input\n                      type=\"text\"\n                      placeholder=\"使用场合\"\n                      value={item.usage}\n                      onChange={(e) => updateExpressionItem(index, 'usage', e.target.value)}\n                      className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    />\n                    <input\n                      type=\"text\"\n                      placeholder=\"例句\"\n                      value={item.example}\n                      onChange={(e) => updateExpressionItem(index, 'example', e.target.value)}\n                      className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    />\n                  </div>\n                  <button\n                    type=\"button\"\n                    onClick={() => removeExpressionItem(index)}\n                    className=\"bg-red-500 text-white px-3 py-1 rounded-md hover:bg-red-600\"\n                  >\n                    删除\n                  </button>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* 作业和备注 */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"bg-yellow-50 p-6 rounded-lg\">\n              <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n                <span className=\"material-icons-outlined text-yellow-600 mr-2\">assignment</span>\n                宿題\n              </h2>\n              <textarea\n                value={homework}\n                onChange={(e) => setHomework(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500\"\n                rows={4}\n                placeholder=\"布置作业内容...\"\n              />\n            </div>\n            <div className=\"bg-blue-50 p-6 rounded-lg\">\n              <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n                <span className=\"material-icons-outlined text-blue-600 mr-2\">note</span>\n                注意事項\n              </h2>\n              <textarea\n                value={notes}\n                onChange={(e) => setNotes(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                rows={4}\n                placeholder=\"学习要点和注意事项...\"\n              />\n            </div>\n          </div>\n\n          {/* 提交按钮 */}\n          <div className=\"flex justify-center\">\n            <button\n              type=\"submit\"\n              className=\"bg-blue-600 text-white px-8 py-3 rounded-md hover:bg-blue-700 flex items-center text-lg font-medium\"\n            >\n              <span className=\"material-icons-outlined mr-2\">publish</span>\n              发布课程\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAkBe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QAChF;YAAE,MAAM;YAAI,SAAS;YAAI,SAAS;QAAG;KACtC;IACD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACvE;YAAE,YAAY;YAAI,SAAS;YAAI,SAAS;YAAI,OAAO;YAAI,SAAS;QAAG;KACpE;IACD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,WAAW;QACf;YAAE,IAAI;YAAY,MAAM;QAAM;QAC9B;YAAE,IAAI;YAAY,MAAM;QAAM;KAC/B;IAED,MAAM,uBAAuB;QAC3B,sBAAsB;eAAI;YAAoB;gBAAE,MAAM;gBAAI,SAAS;gBAAI,SAAS;YAAG;SAAE;IACvF;IAEA,MAAM,oBAAoB;QACxB,mBAAmB;eAAI;YAAiB;gBAAE,YAAY;gBAAI,SAAS;gBAAI,SAAS;gBAAI,OAAO;gBAAI,SAAS;YAAG;SAAE;IAC/G;IAEA,MAAM,0BAA0B,CAAC,OAAe,OAAgC;QAC9E,MAAM,UAAU;eAAI;SAAmB;QACvC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG;QACxB,sBAAsB;IACxB;IAEA,MAAM,uBAAuB,CAAC,OAAe,OAA6B;QACxE,MAAM,UAAU;eAAI;SAAgB;QACpC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG;QACxB,mBAAmB;IACrB;IAEA,MAAM,0BAA0B,CAAC;QAC/B,sBAAsB,mBAAmB,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IAClE;IAEA,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB,gBAAgB,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IAC5D;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,MAAM,aAAa;YACjB,OAAO;YACP,UAAU;YACV,SAAS;gBACP,eAAe;oBACb,OAAO;oBACP,OAAO,mBAAmB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,IAAI;gBACzD;gBACA,aAAa;oBACX,OAAO;oBACP,OAAO,gBAAgB,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC,IAAI;gBAC5D;YACF;YACA;YACA;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,OAAO;gBACP,eAAe;gBACf,oBAAoB,EAAE;gBACtB,sBAAsB;oBAAC;wBAAE,MAAM;wBAAI,SAAS;wBAAI,SAAS;oBAAG;iBAAE;gBAC9D,mBAAmB;oBAAC;wBAAE,YAAY;wBAAI,SAAS;wBAAI,SAAS;wBAAI,OAAO;wBAAI,SAAS;oBAAG;iBAAE;gBACzF,YAAY;gBACZ,SAAS;YACX,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAK,WAAU;sCAA6C;;;;;;wBAAa;;;;;;;8BAI5E,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAA6C;;;;;;wCAAW;;;;;;;8CAG1E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAGZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDAAI,WAAU;8DACZ,SAAS,GAAG,CAAC,CAAA,wBACZ,8OAAC;4DAAuB,WAAU;;8EAChC,8OAAC;oEACC,MAAK;oEACL,SAAS,iBAAiB,QAAQ,CAAC,QAAQ,EAAE;oEAC7C,UAAU,CAAC;wEACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;4EACpB,oBAAoB;mFAAI;gFAAkB,QAAQ,EAAE;6EAAC;wEACvD,OAAO;4EACL,oBAAoB,iBAAiB,MAAM,CAAC,CAAA,KAAM,OAAO,QAAQ,EAAE;wEACrE;oEACF;oEACA,WAAU;;;;;;gEAEX,QAAQ,IAAI;;2DAbH,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAsBhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;gDAAwB;;;;;;;sDAGtF,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAuC;;;;;;gDAAU;;;;;;;;;;;;;8CAIrE,8OAAC;oCAAI,WAAU;8CACZ,mBAAmB,GAAG,CAAC,CAAC,MAAM,sBAC7B,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,KAAK,IAAI;oDAChB,UAAU,CAAC,IAAM,wBAAwB,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACtE,WAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,KAAK,OAAO;oDACnB,UAAU,CAAC,IAAM,wBAAwB,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;oDACzE,WAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,KAAK,OAAO;oDACnB,UAAU,CAAC,IAAM,wBAAwB,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;oDACzE,WAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,wBAAwB;oDACvC,WAAU;8DACX;;;;;;;2CA1BO;;;;;;;;;;;;;;;;sCAmChB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA8C;;;;;;gDAAkB;;;;;;;sDAGlF,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAuC;;;;;;gDAAU;;;;;;;;;;;;;8CAIrE,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,KAAK,UAAU;4DACtB,UAAU,CAAC,IAAM,qBAAqB,OAAO,cAAc,EAAE,MAAM,CAAC,KAAK;4DACzE,WAAU;;;;;;sEAEZ,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,KAAK,OAAO;4DACnB,UAAU,CAAC,IAAM,qBAAqB,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;4DACtE,WAAU;;;;;;sEAEZ,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,KAAK,OAAO;4DACnB,UAAU,CAAC,IAAM,qBAAqB,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;4DACtE,WAAU;;;;;;;;;;;;8DAGd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,KAAK,KAAK;4DACjB,UAAU,CAAC,IAAM,qBAAqB,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK;4DACpE,WAAU;;;;;;sEAEZ,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,KAAK,OAAO;4DACnB,UAAU,CAAC,IAAM,qBAAqB,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;4DACtE,WAAU;;;;;;;;;;;;8DAGd,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,qBAAqB;oDACpC,WAAU;8DACX;;;;;;;2CA5CO;;;;;;;;;;;;;;;;sCAqDhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA+C;;;;;;gDAAiB;;;;;;;sDAGlF,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;4CACV,MAAM;4CACN,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6C;;;;;;gDAAW;;;;;;;sDAG1E,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,WAAU;4CACV,MAAM;4CACN,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;oCAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3E", "debugId": null}}, {"offset": {"line": 700, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}