# 日语学习网站 - 多用户权限系统

这是一个基于 Next.js 的多用户日语学习网站，具备完整的用户权限控制和内容管理功能。

## 🌟 主要功能

### 用户权限控制
- **用户隔离机制**：每个学生只能访问属于自己的学习内容区域
- **路径权限控制**：使用 `/student1/`, `/student2/` 等路径前缀区分不同学生
- **身份验证**：基于 JWT 的登录系统，支持 httpOnly cookie
- **访问控制**：未授权访问自动重定向到登录页面或返回 404

### 内容管理
- **动态页面管理**：支持为每个学生动态添加和删除学习页面
- **自动导航生成**：根据用户权限自动生成专属导航菜单
- **内容隔离**：不同学生之间的内容完全隔离，互不可见

### 学习内容
- **日语学习笔记**：包含发音练习、词汇学习等内容
- **词汇对照表**：日语-中文词汇对照
- **可扩展内容**：支持添加更多学习模块

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 访问网站
打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 👤 测试账户

系统预设了两个测试账户：

| 用户名 | 密码 | 可访问内容 |
|--------|------|------------|
| student1 | password | CET6学习笔记、词汇表、词汇练习 |
| student2 | password | 语法学习、阅读理解 |

## 📁 项目结构

```
japanese-learning-site/
├── src/
│   ├── app/
│   │   ├── [student]/           # 动态学生路由
│   │   │   ├── [page]/          # 动态页面路由
│   │   │   ├── manage/          # 页面管理界面
│   │   │   └── page.tsx         # 学生主页
│   │   ├── api/
│   │   │   ├── auth/            # 身份验证API
│   │   │   └── pages/           # 页面管理API
│   │   ├── login/               # 登录页面
│   │   └── layout.tsx           # 根布局
│   ├── components/
│   │   ├── Layout.tsx           # 主布局组件
│   │   ├── StudyNotes.tsx       # 学习笔记组件
│   │   └── VocabularyTable.tsx  # 词汇表组件
│   ├── lib/
│   │   └── auth.ts              # 身份验证工具
│   └── middleware.ts            # 权限控制中间件
├── data/
│   ├── users.json               # 用户数据
│   └── content/
│       ├── student1/            # 学生1的内容配置
│       └── student2/            # 学生2的内容配置
└── .env.local                   # 环境变量
```

## 🔧 配置说明

### 环境变量
在 `.env.local` 文件中配置：
```
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
NEXT_PUBLIC_APP_NAME=日语学习网站
```

### 用户管理
用户数据存储在 `data/users.json` 中：
```json
{
  "student1": {
    "id": "student1",
    "username": "student1",
    "password": "$2a$10$...",  // bcrypt加密后的密码
    "name": "学生一",
    "pages": ["cet6", "jp", "vocabulary"]
  }
}
```

### 页面配置
每个学生的页面配置存储在 `data/content/{studentId}/pages.json` 中：
```json
{
  "cet6": {
    "title": "日语学习回顾",
    "description": "根据老师消息整理的学习笔记",
    "icon": "school",
    "type": "study-notes"
  }
}
```

## 🛠️ 功能使用

### 添加新学生
1. 在 `data/users.json` 中添加新用户
2. 创建对应的内容目录 `data/content/{studentId}/`
3. 添加页面配置文件 `pages.json`

### 动态管理页面
1. 登录后访问 `/{studentId}/manage`
2. 使用界面添加或删除页面
3. 页面会自动出现在导航菜单中

### 自定义学习内容
1. 在 `src/components/` 中创建新的内容组件
2. 在 `src/app/[student]/[page]/page.tsx` 中添加渲染逻辑
3. 通过页面管理界面添加新页面

## 🔒 安全特性

- **JWT Token**：使用 httpOnly cookie 存储，防止 XSS 攻击
- **路径验证**：中间件验证用户权限，防止越权访问
- **密码加密**：使用 bcrypt 加密存储用户密码
- **CSRF 保护**：使用 SameSite cookie 属性

## 🚀 部署建议

1. **生产环境**：
   - 更改 `JWT_SECRET` 为强密码
   - 使用真实的数据库替代 JSON 文件
   - 启用 HTTPS

2. **扩展功能**：
   - 添加用户注册功能
   - 集成数据库（PostgreSQL/MongoDB）
   - 添加文件上传功能
   - 实现学习进度跟踪

## 📝 技术栈

- **框架**：Next.js 15 (App Router)
- **样式**：Tailwind CSS
- **身份验证**：JWT + bcrypt
- **图标**：Material Icons
- **字体**：Inter + Noto Sans JP
- **语言**：TypeScript

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！
