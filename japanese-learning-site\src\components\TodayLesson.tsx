interface PronunciationItem {
  word: string
  reading: string
  meaning: string
  audio?: string
}

interface ExpressionItem {
  expression: string
  reading: string
  meaning: string
  usage: string
  example: string
}

interface LessonContent {
  pronunciation: {
    title: string
    items: PronunciationItem[]
  }
  expressions: {
    title: string
    items: ExpressionItem[]
  }
}

interface TodayLessonProps {
  lesson?: {
    id: string
    title: string
    date: string
    content: LessonContent
    homework?: string
    notes?: string
  }
}

export default function TodayLesson({ lesson }: TodayLessonProps) {
  // 默认示例数据
  const defaultLesson = {
    id: "sample",
    title: "2024年1月15日 - 日常问候",
    date: "2024-01-15",
    content: {
      pronunciation: {
        title: "今日の発音",
        items: [
          {
            word: "おはようございます",
            reading: "ohayou gozaimasu",
            meaning: "早上好（敬语）"
          },
          {
            word: "こんにちは", 
            reading: "konnichiwa",
            meaning: "你好（下午用）"
          },
          {
            word: "こんばんは",
            reading: "konbanwa", 
            meaning: "晚上好"
          }
        ]
      },
      expressions: {
        title: "今日の表現",
        items: [
          {
            expression: "お疲れ様でした",
            reading: "otsukaresama deshita",
            meaning: "辛苦了",
            usage: "工作结束时对同事说",
            example: "今日もお疲れ様でした。"
          },
          {
            expression: "よろしくお願いします",
            reading: "yoroshiku onegaishimasu", 
            meaning: "请多关照",
            usage: "初次见面或请求帮助时",
            example: "初めまして、よろしくお願いします。"
          }
        ]
      }
    },
    homework: "练习今天学习的问候语，明天课堂上演示",
    notes: "注意敬语的使用场合和时间"
  }

  const currentLesson = lesson || defaultLesson

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-8">
        {/* 课程标题 */}
        <div className="border-b border-gray-200 pb-6 mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center">
            <span className="material-icons-outlined text-blue-600 mr-3">today</span>
            今日の授業
          </h1>
          <p className="text-lg text-gray-600">{currentLesson.title}</p>
          <p className="text-sm text-gray-500 mt-1">
            {new Date(currentLesson.date).toLocaleDateString('ja-JP')}
          </p>
        </div>

        {/* 发音练习 */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
            <span className="material-icons-outlined text-red-500 mr-3">record_voice_over</span>
            {currentLesson.content.pronunciation.title}
          </h2>
          <div className="grid gap-4">
            {currentLesson.content.pronunciation.items.map((item, index) => (
              <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-red-800 japanese-text mb-2">
                      {item.word}
                    </h3>
                    <p className="text-red-600 font-medium mb-1">
                      [{item.reading}]
                    </p>
                    <p className="text-gray-700">
                      {item.meaning}
                    </p>
                  </div>
                  <button className="bg-red-600 text-white p-3 rounded-full hover:bg-red-700 transition-colors">
                    <span className="material-icons-outlined">volume_up</span>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* 表达练习 */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
            <span className="material-icons-outlined text-green-500 mr-3">chat_bubble</span>
            {currentLesson.content.expressions.title}
          </h2>
          <div className="grid gap-6">
            {currentLesson.content.expressions.items.map((item, index) => (
              <div key={index} className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-xl font-bold text-green-800 japanese-text mb-2">
                      {item.expression}
                    </h3>
                    <p className="text-green-600 font-medium mb-2">
                      [{item.reading}]
                    </p>
                    <p className="text-gray-700 mb-3">
                      <strong>意思：</strong>{item.meaning}
                    </p>
                    <p className="text-gray-600 text-sm">
                      <strong>使用场合：</strong>{item.usage}
                    </p>
                  </div>
                  <div className="bg-white p-4 rounded border border-green-300">
                    <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                      <span className="material-icons-outlined text-green-600 mr-2 text-sm">format_quote</span>
                      例句
                    </h4>
                    <p className="japanese-text text-green-800 font-medium">
                      {item.example}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* 作业和备注 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {currentLesson.homework && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                <span className="material-icons-outlined text-yellow-600 mr-2">assignment</span>
                宿題
              </h3>
              <p className="text-gray-700">{currentLesson.homework}</p>
            </div>
          )}
          
          {currentLesson.notes && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                <span className="material-icons-outlined text-blue-600 mr-2">note</span>
                注意事項
              </h3>
              <p className="text-gray-700">{currentLesson.notes}</p>
            </div>
          )}
        </div>

        {/* 学习进度 */}
        <div className="mt-8 bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <span className="material-icons-outlined text-purple-600 mr-2">trending_up</span>
            学習進捗
          </h3>
          <div className="flex items-center space-x-4">
            <button className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center">
              <span className="material-icons-outlined mr-2">check_circle</span>
              完了
            </button>
            <button className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 flex items-center">
              <span className="material-icons-outlined mr-2">bookmark</span>
              保存
            </button>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center">
              <span className="material-icons-outlined mr-2">quiz</span>
              練習
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
