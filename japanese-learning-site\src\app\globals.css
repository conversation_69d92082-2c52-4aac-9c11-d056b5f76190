@import "tailwindcss";

:root {
  --background: #f8fafc;
  --foreground: #374151;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter), var(--font-noto-sans-jp);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), var(--font-noto-sans-jp), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.7;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 日语文本样式 */
.japanese-text {
  font-family: var(--font-noto-sans-jp), sans-serif;
}

/* Material Icons 样式调整 */
.material-icons-outlined {
  font-feature-settings: 'liga';
  vertical-align: middle;
}
