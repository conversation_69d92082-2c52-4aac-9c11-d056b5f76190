export default function VocabularyTable() {
  const vocabularyData = [
    { japanese: "エレベーター", chinese: "电梯" },
    { japanese: "エスカレーター", chinese: "自动扶梯" },
    { japanese: "つけっぱなし", chinese: "（电器等）一直开着，未关闭" },
    { japanese: "できるだけ", chinese: "尽可能，尽量" },
    { japanese: "一週間 (いっしゅうかん)", chinese: "一星期，一周" },
    { japanese: "わたしたち", chinese: "我们" },
    { japanese: "〜なかで", chinese: "在...之中" },
    { japanese: "派遣会社 (はけんがいしゃ)", chinese: "人才派遣公司" },
    { japanese: "メッセージ", chinese: "消息，信息，留言" },
    { japanese: "恥ずかしがるので (はずかしがるので)", chinese: "因为（他/她）害羞" },
    { japanese: "合併されちゃって (がっぺいされちゃって)", chinese: "（公司等）被合并了（口语形式）" }
  ]

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-6 flex items-center">
          <span className="material-icons-outlined text-blue-600 mr-3">table_view</span>
          <span className="text-blue-600">日语词汇</span>
          <span className="text-gray-600 ml-2">对照表</span>
        </h1>
        
        <div className="overflow-x-auto">
          <table className="w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm">
            <thead>
              <tr className="bg-gray-50">
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">
                  <div className="flex items-center">
                    <span className="material-icons-outlined text-red-600 mr-2">translate</span>
                    日语
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">
                  <div className="flex items-center">
                    <span className="material-icons-outlined text-green-600 mr-2">g_translate</span>
                    中文含义
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              {vocabularyData.map((item, index) => (
                <tr key={index} className="hover:bg-blue-50 transition-colors">
                  <td className="px-6 py-4 border-b font-medium text-blue-800 text-lg">
                    {item.japanese}
                  </td>
                  <td className="px-6 py-4 border-b text-gray-700">
                    {item.chinese}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        <div className="mt-8 text-center text-sm text-gray-600 bg-gray-50 p-4 rounded-lg">
          <p>共收录 {vocabularyData.length} 个词汇</p>
        </div>
      </div>
    </div>
  )
}
