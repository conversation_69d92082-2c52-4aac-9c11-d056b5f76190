export default function GrammarLesson() {
  const grammarPoints = [
    {
      pattern: "〜です/〜である",
      meaning: "是，表示断定",
      example: "私は学生です。",
      translation: "我是学生。"
    },
    {
      pattern: "〜ます/〜ません",
      meaning: "敬语动词变位",
      example: "毎日勉強します。",
      translation: "每天学习。"
    },
    {
      pattern: "〜が好きです",
      meaning: "喜欢...",
      example: "日本語が好きです。",
      translation: "喜欢日语。"
    }
  ]

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-6 flex items-center">
          <span className="material-icons-outlined text-blue-600 mr-3">auto_stories</span>
          日语语法学习
        </h1>
        
        <div className="space-y-6">
          {grammarPoints.map((point, index) => (
            <div key={index} className="bg-gray-50 p-6 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-lg font-semibold text-blue-800 mb-2">
                    {point.pattern}
                  </h3>
                  <p className="text-gray-700 mb-3">{point.meaning}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">例句：</h4>
                  <p className="japanese-text text-blue-800 mb-1">{point.example}</p>
                  <p className="text-gray-600 text-sm">{point.translation}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 bg-blue-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold text-gray-900 mb-3">练习</h2>
          <p className="text-gray-700">
            请使用上述语法点造句，并在下次课堂上分享。
          </p>
        </div>
      </div>
    </div>
  )
}
