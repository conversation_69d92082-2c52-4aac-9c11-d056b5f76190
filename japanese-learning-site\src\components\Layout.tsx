'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface LayoutProps {
  children: React.ReactNode
  studentId: string
  currentPage?: string
  pages: Record<string, {
    title: string
    description: string
    icon: string
    type: string
  }>
}

export default function Layout({ children, studentId, currentPage, pages }: LayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const router = useRouter()

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' })
      router.push('/login')
    } catch (error) {
      console.error('登出失败:', error)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              >
                <span className="material-icons-outlined">menu</span>
              </button>
              <h1 className="ml-2 text-xl font-semibold text-gray-900">
                日语学习网站
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">欢迎，{studentId}</span>
              <button
                onClick={handleLogout}
                className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
              >
                <span className="material-icons-outlined mr-1 text-sm">logout</span>
                登出
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* 侧边栏 */}
        <aside className={`${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } md:translate-x-0 fixed md:static inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-200 ease-in-out`}>
          <div className="flex flex-col h-full">
            <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
              <div className="flex items-center flex-shrink-0 px-4">
                <span className="material-icons-outlined text-blue-600 mr-2">person</span>
                <span className="text-lg font-medium text-gray-900">{studentId} 的学习空间</span>
              </div>
              
              <nav className="mt-8 flex-1 px-2 space-y-1">
                {Object.entries(pages).map(([pageId, pageInfo]) => (
                  <Link
                    key={pageId}
                    href={`/${studentId}/${pageId}`}
                    className={`${
                      currentPage === pageId
                        ? 'bg-blue-100 text-blue-900 border-r-2 border-blue-600'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    } group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors`}
                  >
                    <span className="material-icons-outlined mr-3 text-lg">
                      {pageInfo.icon}
                    </span>
                    <div>
                      <div className="font-medium">{pageInfo.title}</div>
                      <div className="text-xs text-gray-500">{pageInfo.description}</div>
                    </div>
                  </Link>
                ))}
              </nav>
            </div>
          </div>
        </aside>

        {/* 遮罩层 (移动端) */}
        {sidebarOpen && (
          <div
            className="md:hidden fixed inset-0 z-40 bg-gray-600 bg-opacity-75"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* 主内容区域 */}
        <main className="flex-1 md:ml-0">
          <div className="py-6 px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
