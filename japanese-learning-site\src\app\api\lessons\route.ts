import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/lib/auth'
import fs from 'fs'
import path from 'path'

// 获取课程列表
export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value
    
    if (!token) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }

    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: '无效token' }, { status: 401 })
    }

    const lessonsPath = path.join(process.cwd(), 'data', 'lessons.json')
    
    if (!fs.existsSync(lessonsPath)) {
      return NextResponse.json({ lessons: [] })
    }

    const lessonsData = fs.readFileSync(lessonsPath, 'utf8')
    const { lessons } = JSON.parse(lessonsData)

    // 根据用户角色过滤课程
    const userLessons = lessons.filter((lesson: any) => 
      lesson.students.includes(decoded.userId) || lesson.teacher === decoded.userId
    )

    return NextResponse.json({ lessons: userLessons })
  } catch (error) {
    console.error('获取课程列表错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// 发布新课程
export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value
    
    if (!token) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }

    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: '无效token' }, { status: 401 })
    }

    // 验证是否为教师
    const usersPath = path.join(process.cwd(), 'data', 'users.json')
    const usersData = fs.readFileSync(usersPath, 'utf8')
    const users = JSON.parse(usersData)
    const user = users[decoded.userId]

    if (!user || user.role !== 'teacher') {
      return NextResponse.json({ error: '只有教师可以发布课程' }, { status: 403 })
    }

    const lessonData = await request.json()
    
    // 生成课程ID
    const lessonId = `lesson_${Date.now()}`
    const now = new Date().toISOString()

    const newLesson = {
      id: lessonId,
      title: lessonData.title,
      date: new Date().toISOString().split('T')[0],
      teacher: decoded.userId,
      students: lessonData.students,
      content: lessonData.content,
      homework: lessonData.homework || '',
      notes: lessonData.notes || '',
      createdAt: now,
      updatedAt: now
    }

    // 读取现有课程数据
    const lessonsPath = path.join(process.cwd(), 'data', 'lessons.json')
    let lessonsFile = { lessons: [] }
    
    if (fs.existsSync(lessonsPath)) {
      const lessonsData = fs.readFileSync(lessonsPath, 'utf8')
      lessonsFile = JSON.parse(lessonsData)
    }

    // 添加新课程
    lessonsFile.lessons.unshift(newLesson)

    // 保存课程数据
    fs.writeFileSync(lessonsPath, JSON.stringify(lessonsFile, null, 2))

    // 为每个学生添加课程页面
    for (const studentId of lessonData.students) {
      const studentPagesPath = path.join(process.cwd(), 'data', 'content', studentId, 'pages.json')
      
      if (fs.existsSync(studentPagesPath)) {
        const pagesData = fs.readFileSync(studentPagesPath, 'utf8')
        const pages = JSON.parse(pagesData)
        
        // 添加今日课程页面
        pages[`lesson-${lessonId}`] = {
          title: "今日の授業",
          description: lessonData.title,
          icon: "today",
          type: "lesson",
          lessonId: lessonId
        }

        fs.writeFileSync(studentPagesPath, JSON.stringify(pages, null, 2))
      }
    }

    return NextResponse.json({ 
      success: true, 
      lesson: newLesson,
      message: '课程发布成功！'
    })
  } catch (error) {
    console.error('发布课程错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
