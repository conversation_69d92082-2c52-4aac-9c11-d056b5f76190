{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\nimport jwt from 'jsonwebtoken'\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  // 允许访问登录页面和API路由\n  if (pathname === '/login' || pathname.startsWith('/api/')) {\n    return NextResponse.next()\n  }\n\n  // 检查是否访问学生专属路径\n  const studentPathMatch = pathname.match(/^\\/([^\\/]+)/)\n  if (studentPathMatch) {\n    const requestedStudent = studentPathMatch[1]\n    \n    // 获取JWT token\n    const token = request.cookies.get('auth-token')?.value\n\n    if (!token) {\n      // 未登录，重定向到登录页\n      return NextResponse.redirect(new URL('/login', request.url))\n    }\n\n    try {\n      // 验证JWT token\n      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as { userId: string }\n      \n      // 检查用户是否有权限访问该路径\n      if (decoded.userId !== requestedStudent) {\n        // 无权限访问，返回404\n        return NextResponse.rewrite(new URL('/404', request.url))\n      }\n\n      return NextResponse.next()\n    } catch (error) {\n      // Token无效，重定向到登录页\n      return NextResponse.redirect(new URL('/login', request.url))\n    }\n  }\n\n  // 根路径重定向到登录页\n  if (pathname === '/') {\n    return NextResponse.redirect(new URL('/login', request.url))\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * 匹配所有路径除了:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;;;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,iBAAiB;IACjB,IAAI,aAAa,YAAY,SAAS,UAAU,CAAC,UAAU;QACzD,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,eAAe;IACf,MAAM,mBAAmB,SAAS,KAAK,CAAC;IACxC,IAAI,kBAAkB;QACpB,MAAM,mBAAmB,gBAAgB,CAAC,EAAE;QAE5C,cAAc;QACd,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,cAAc;YACd,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC5D;QAEA,IAAI;YACF,cAAc;YACd,MAAM,UAAU,6IAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU;YAExD,iBAAiB;YACjB,IAAI,QAAQ,MAAM,KAAK,kBAAkB;gBACvC,cAAc;gBACd,OAAO,6LAAA,CAAA,eAAY,CAAC,OAAO,CAAC,IAAI,IAAI,QAAQ,QAAQ,GAAG;YACzD;YAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;QAC1B,EAAE,OAAO,OAAO;YACd,kBAAkB;YAClB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC5D;IACF;IAEA,aAAa;IACb,IAAI,aAAa,KAAK;QACpB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;IAC5D;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}