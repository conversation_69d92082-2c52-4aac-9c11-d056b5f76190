{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken'\nimport bcrypt from 'bcryptjs'\nimport fs from 'fs'\nimport path from 'path'\n\nexport interface User {\n  id: string\n  username: string\n  password: string\n  name: string\n  role?: string\n  teacher?: string\n  students?: string[]\n  pages?: string[]\n}\n\nexport interface UserData {\n  [key: string]: User\n}\n\n// 获取用户数据\nexport function getUserData(): UserData {\n  const usersPath = path.join(process.cwd(), 'data', 'users.json')\n  const usersData = fs.readFileSync(usersPath, 'utf8')\n  return JSON.parse(usersData)\n}\n\n// 验证用户凭据\nexport async function validateUser(username: string, password: string): Promise<User | null> {\n  const users = getUserData()\n  const user = Object.values(users).find(u => u.username === username)\n\n  if (!user) {\n    return null\n  }\n\n  const isValid = await bcrypt.compare(password, user.password)\n  return isValid ? user : null\n}\n\n// 生成JWT token\nexport function generateToken(userId: string): string {\n  return jwt.sign({ userId }, process.env.JWT_SECRET!, { expiresIn: '7d' })\n}\n\n// 验证JWT token\nexport function verifyToken(token: string): { userId: string } | null {\n  try {\n    return jwt.verify(token, process.env.JWT_SECRET!) as { userId: string }\n  } catch {\n    return null\n  }\n}\n\n// 获取用户页面配置\nexport function getUserPages(userId: string) {\n  try {\n    const pagesPath = path.join(process.cwd(), 'data', 'content', userId, 'pages.json')\n    const pagesData = fs.readFileSync(pagesPath, 'utf8')\n    return JSON.parse(pagesData)\n  } catch {\n    return {}\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;AAkBO,SAAS;IACd,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;IACnD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;IAC7C,OAAO,KAAK,KAAK,CAAC;AACpB;AAGO,eAAe,aAAa,QAAgB,EAAE,QAAgB;IACnE,MAAM,QAAQ;IACd,MAAM,OAAO,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IAE3D,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,UAAU,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ;IAC5D,OAAO,UAAU,OAAO;AAC1B;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC;QAAE;IAAO,GAAG,QAAQ,GAAG,CAAC,UAAU,EAAG;QAAE,WAAW;IAAK;AACzE;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU;IACjD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,aAAa,MAAc;IACzC,IAAI;QACF,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,WAAW,QAAQ;QACtE,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;QAC7C,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO,CAAC;IACV;AACF", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/app/api/students/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { verifyToken } from '@/lib/auth'\nimport bcrypt from 'bcryptjs'\nimport fs from 'fs'\nimport path from 'path'\n\n// 获取单个学生详细信息\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params\n    const token = request.cookies.get('auth-token')?.value\n    \n    if (!token) {\n      return NextResponse.json({ error: '未授权' }, { status: 401 })\n    }\n\n    const decoded = verifyToken(token)\n    if (!decoded) {\n      return NextResponse.json({ error: '无效token' }, { status: 401 })\n    }\n\n    // 验证是否为教师\n    const usersPath = path.join(process.cwd(), 'data', 'users.json')\n    const usersData = fs.readFileSync(usersPath, 'utf8')\n    const users = JSON.parse(usersData)\n    const user = users[decoded.userId]\n\n    if (!user || user.role !== 'teacher') {\n      return NextResponse.json({ error: '只有教师可以查看学生详情' }, { status: 403 })\n    }\n\n    // 获取学生信息\n    const student = users[id]\n    if (!student || student.role !== 'student' || student.teacher !== decoded.userId) {\n      return NextResponse.json({ error: '学生不存在或无权限访问' }, { status: 404 })\n    }\n\n    // 获取学生的课程信息\n    const lessonsPath = path.join(process.cwd(), 'data', 'lessons.json')\n    let studentLessons = []\n    \n    if (fs.existsSync(lessonsPath)) {\n      const lessonsData = fs.readFileSync(lessonsPath, 'utf8')\n      const { lessons } = JSON.parse(lessonsData)\n      studentLessons = lessons.filter((lesson: any) => lesson.students.includes(id))\n    }\n\n    return NextResponse.json({\n      student: {\n        id: student.id,\n        username: student.username,\n        name: student.name,\n        role: student.role,\n        teacher: student.teacher,\n        evaluation: student.evaluation || '',\n        createdAt: student.createdAt || new Date().toISOString()\n      },\n      lessons: studentLessons\n    })\n  } catch (error) {\n    console.error('获取学生详情错误:', error)\n    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })\n  }\n}\n\n// 更新学生信息\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params\n    const token = request.cookies.get('auth-token')?.value\n    \n    if (!token) {\n      return NextResponse.json({ error: '未授权' }, { status: 401 })\n    }\n\n    const decoded = verifyToken(token)\n    if (!decoded) {\n      return NextResponse.json({ error: '无效token' }, { status: 401 })\n    }\n\n    // 验证是否为教师\n    const usersPath = path.join(process.cwd(), 'data', 'users.json')\n    const usersData = fs.readFileSync(usersPath, 'utf8')\n    const users = JSON.parse(usersData)\n    const user = users[decoded.userId]\n\n    if (!user || user.role !== 'teacher') {\n      return NextResponse.json({ error: '只有教师可以修改学生信息' }, { status: 403 })\n    }\n\n    // 检查学生是否存在\n    const student = users[id]\n    if (!student || student.role !== 'student' || student.teacher !== decoded.userId) {\n      return NextResponse.json({ error: '学生不存在或无权限修改' }, { status: 404 })\n    }\n\n    const updateData = await request.json()\n    const { name, username, password, evaluation } = updateData\n\n    // 验证输入\n    if (name !== undefined && !name.trim()) {\n      return NextResponse.json({ error: '姓名不能为空' }, { status: 400 })\n    }\n\n    if (username !== undefined && !username.trim()) {\n      return NextResponse.json({ error: '用户名不能为空' }, { status: 400 })\n    }\n\n    // 检查用户名是否已被其他用户使用\n    if (username && username.trim() !== student.username) {\n      const trimmedUsername = username.trim()\n      const existingUser = Object.values(users).find((u: any) => \n        u.username === trimmedUsername && u.id !== id\n      )\n      if (existingUser) {\n        return NextResponse.json({ error: '用户名已被使用' }, { status: 400 })\n      }\n      student.username = trimmedUsername\n    }\n\n    // 更新学生信息\n    if (name !== undefined) {\n      student.name = name.trim()\n    }\n\n    if (evaluation !== undefined) {\n      student.evaluation = evaluation.trim()\n    }\n\n    if (password && password.trim()) {\n      const hashedPassword = await bcrypt.hash(password.trim(), 10)\n      student.password = hashedPassword\n    }\n\n    student.updatedAt = new Date().toISOString()\n\n    // 保存更新后的数据\n    fs.writeFileSync(usersPath, JSON.stringify(users, null, 2))\n\n    return NextResponse.json({\n      success: true,\n      student: {\n        id: student.id,\n        username: student.username,\n        name: student.name,\n        role: student.role,\n        teacher: student.teacher,\n        evaluation: student.evaluation || '',\n        updatedAt: student.updatedAt\n      },\n      message: '学生信息更新成功！'\n    })\n  } catch (error) {\n    console.error('更新学生信息错误:', error)\n    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })\n  }\n}\n\n// 删除学生\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params\n    const token = request.cookies.get('auth-token')?.value\n    \n    if (!token) {\n      return NextResponse.json({ error: '未授权' }, { status: 401 })\n    }\n\n    const decoded = verifyToken(token)\n    if (!decoded) {\n      return NextResponse.json({ error: '无效token' }, { status: 401 })\n    }\n\n    // 验证是否为教师\n    const usersPath = path.join(process.cwd(), 'data', 'users.json')\n    const usersData = fs.readFileSync(usersPath, 'utf8')\n    const users = JSON.parse(usersData)\n    const user = users[decoded.userId]\n\n    if (!user || user.role !== 'teacher') {\n      return NextResponse.json({ error: '只有教师可以删除学生' }, { status: 403 })\n    }\n\n    // 检查学生是否存在\n    const student = users[id]\n    if (!student || student.role !== 'student' || student.teacher !== decoded.userId) {\n      return NextResponse.json({ error: '学生不存在或无权限删除' }, { status: 404 })\n    }\n\n    // 删除学生\n    delete users[id]\n\n    // 保存更新后的数据\n    fs.writeFileSync(usersPath, JSON.stringify(users, null, 2))\n\n    // 删除学生的内容目录\n    const studentContentDir = path.join(process.cwd(), 'data', 'content', id)\n    if (fs.existsSync(studentContentDir)) {\n      fs.rmSync(studentContentDir, { recursive: true, force: true })\n    }\n\n    return NextResponse.json({\n      success: true,\n      message: '学生删除成功！'\n    })\n  } catch (error) {\n    console.error('删除学生错误:', error)\n    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAM,GAAG;gBAAE,QAAQ;YAAI;QAC3D;QAEA,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAU,GAAG;gBAAE,QAAQ;YAAI;QAC/D;QAEA,UAAU;QACV,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;QACnD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;QAC7C,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,MAAM,OAAO,KAAK,CAAC,QAAQ,MAAM,CAAC;QAElC,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,WAAW;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,SAAS;QACT,MAAM,UAAU,KAAK,CAAC,GAAG;QACzB,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,aAAa,QAAQ,OAAO,KAAK,QAAQ,MAAM,EAAE;YAChF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAc,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,YAAY;QACZ,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;QACrD,IAAI,iBAAiB,EAAE;QAEvB,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,cAAc;YAC9B,MAAM,cAAc,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,aAAa;YACjD,MAAM,EAAE,OAAO,EAAE,GAAG,KAAK,KAAK,CAAC;YAC/B,iBAAiB,QAAQ,MAAM,CAAC,CAAC,SAAgB,OAAO,QAAQ,CAAC,QAAQ,CAAC;QAC5E;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;gBACP,IAAI,QAAQ,EAAE;gBACd,UAAU,QAAQ,QAAQ;gBAC1B,MAAM,QAAQ,IAAI;gBAClB,MAAM,QAAQ,IAAI;gBAClB,SAAS,QAAQ,OAAO;gBACxB,YAAY,QAAQ,UAAU,IAAI;gBAClC,WAAW,QAAQ,SAAS,IAAI,IAAI,OAAO,WAAW;YACxD;YACA,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAU,GAAG;YAAE,QAAQ;QAAI;IAC/D;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAM,GAAG;gBAAE,QAAQ;YAAI;QAC3D;QAEA,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAU,GAAG;gBAAE,QAAQ;YAAI;QAC/D;QAEA,UAAU;QACV,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;QACnD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;QAC7C,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,MAAM,OAAO,KAAK,CAAC,QAAQ,MAAM,CAAC;QAElC,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,WAAW;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,WAAW;QACX,MAAM,UAAU,KAAK,CAAC,GAAG;QACzB,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,aAAa,QAAQ,OAAO,KAAK,QAAQ,MAAM,EAAE;YAChF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAc,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,MAAM,aAAa,MAAM,QAAQ,IAAI;QACrC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG;QAEjD,OAAO;QACP,IAAI,SAAS,aAAa,CAAC,KAAK,IAAI,IAAI;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAS,GAAG;gBAAE,QAAQ;YAAI;QAC9D;QAEA,IAAI,aAAa,aAAa,CAAC,SAAS,IAAI,IAAI;YAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAU,GAAG;gBAAE,QAAQ;YAAI;QAC/D;QAEA,kBAAkB;QAClB,IAAI,YAAY,SAAS,IAAI,OAAO,QAAQ,QAAQ,EAAE;YACpD,MAAM,kBAAkB,SAAS,IAAI;YACrC,MAAM,eAAe,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC,IAC9C,EAAE,QAAQ,KAAK,mBAAmB,EAAE,EAAE,KAAK;YAE7C,IAAI,cAAc;gBAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAU,GAAG;oBAAE,QAAQ;gBAAI;YAC/D;YACA,QAAQ,QAAQ,GAAG;QACrB;QAEA,SAAS;QACT,IAAI,SAAS,WAAW;YACtB,QAAQ,IAAI,GAAG,KAAK,IAAI;QAC1B;QAEA,IAAI,eAAe,WAAW;YAC5B,QAAQ,UAAU,GAAG,WAAW,IAAI;QACtC;QAEA,IAAI,YAAY,SAAS,IAAI,IAAI;YAC/B,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI;YAC1D,QAAQ,QAAQ,GAAG;QACrB;QAEA,QAAQ,SAAS,GAAG,IAAI,OAAO,WAAW;QAE1C,WAAW;QACX,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,WAAW,KAAK,SAAS,CAAC,OAAO,MAAM;QAExD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;gBACP,IAAI,QAAQ,EAAE;gBACd,UAAU,QAAQ,QAAQ;gBAC1B,MAAM,QAAQ,IAAI;gBAClB,MAAM,QAAQ,IAAI;gBAClB,SAAS,QAAQ,OAAO;gBACxB,YAAY,QAAQ,UAAU,IAAI;gBAClC,WAAW,QAAQ,SAAS;YAC9B;YACA,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAU,GAAG;YAAE,QAAQ;QAAI;IAC/D;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAM,GAAG;gBAAE,QAAQ;YAAI;QAC3D;QAEA,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAU,GAAG;gBAAE,QAAQ;YAAI;QAC/D;QAEA,UAAU;QACV,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;QACnD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;QAC7C,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,MAAM,OAAO,KAAK,CAAC,QAAQ,MAAM,CAAC;QAElC,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,WAAW;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAa,GAAG;gBAAE,QAAQ;YAAI;QAClE;QAEA,WAAW;QACX,MAAM,UAAU,KAAK,CAAC,GAAG;QACzB,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,aAAa,QAAQ,OAAO,KAAK,QAAQ,MAAM,EAAE;YAChF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAc,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,OAAO;QACP,OAAO,KAAK,CAAC,GAAG;QAEhB,WAAW;QACX,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,WAAW,KAAK,SAAS,CAAC,OAAO,MAAM;QAExD,YAAY;QACZ,MAAM,oBAAoB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,WAAW;QACtE,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,oBAAoB;YACpC,6FAAA,CAAA,UAAE,CAAC,MAAM,CAAC,mBAAmB;gBAAE,WAAW;gBAAM,OAAO;YAAK;QAC9D;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAU,GAAG;YAAE,QAAQ;QAAI;IAC/D;AACF", "debugId": null}}]}