'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Layout from '@/components/Layout'

interface PageInfo {
  title: string
  description: string
  icon: string
  type: string
}

export default function ManagePage() {
  const params = useParams()
  const studentId = params.student as string
  const [pages, setPages] = useState<Record<string, PageInfo>>({})
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [newPage, setNewPage] = useState({
    pageId: '',
    title: '',
    description: '',
    icon: 'article',
    type: 'custom'
  })

  const iconOptions = [
    { value: 'article', label: '文章' },
    { value: 'quiz', label: '测验' },
    { value: 'school', label: '学习' },
    { value: 'table_view', label: '表格' },
    { value: 'auto_stories', label: '故事' },
    { value: 'menu_book', label: '书籍' },
    { value: 'translate', label: '翻译' },
    { value: 'psychology', label: '心理学' }
  ]

  useEffect(() => {
    fetchPages()
  }, [])

  const fetchPages = async () => {
    try {
      const response = await fetch('/api/pages')
      const data = await response.json()
      if (data.pages) {
        setPages(data.pages)
      }
    } catch (error) {
      console.error('获取页面列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddPage = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!newPage.pageId || !newPage.title) {
      alert('页面ID和标题不能为空')
      return
    }

    try {
      const response = await fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newPage),
      })

      const data = await response.json()
      
      if (data.success) {
        setPages(data.pages)
        setNewPage({
          pageId: '',
          title: '',
          description: '',
          icon: 'article',
          type: 'custom'
        })
        setShowAddForm(false)
        alert('页面添加成功！')
      } else {
        alert(data.error || '添加页面失败')
      }
    } catch (error) {
      console.error('添加页面失败:', error)
      alert('添加页面失败')
    }
  }

  const handleDeletePage = async (pageId: string) => {
    if (!confirm(`确定要删除页面 "${pages[pageId]?.title}" 吗？`)) {
      return
    }

    try {
      const response = await fetch('/api/pages', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pageId }),
      })

      const data = await response.json()
      
      if (data.success) {
        setPages(data.pages)
        alert('页面删除成功！')
      } else {
        alert(data.error || '删除页面失败')
      }
    } catch (error) {
      console.error('删除页面失败:', error)
      alert('删除页面失败')
    }
  }

  if (loading) {
    return (
      <Layout studentId={studentId} pages={pages}>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-gray-600">加载中...</div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout studentId={studentId} pages={pages}>
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <span className="material-icons-outlined text-blue-600 mr-3">settings</span>
              页面管理
            </h1>
            <button
              onClick={() => setShowAddForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
            >
              <span className="material-icons-outlined mr-2">add</span>
              添加页面
            </button>
          </div>

          {/* 添加页面表单 */}
          {showAddForm && (
            <div className="mb-8 bg-gray-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">添加新页面</h2>
              <form onSubmit={handleAddPage} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      页面ID (URL路径)
                    </label>
                    <input
                      type="text"
                      value={newPage.pageId}
                      onChange={(e) => setNewPage({...newPage, pageId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="例如: my-new-page"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      页面标题
                    </label>
                    <input
                      type="text"
                      value={newPage.title}
                      onChange={(e) => setNewPage({...newPage, title: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="页面标题"
                      required
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    页面描述
                  </label>
                  <input
                    type="text"
                    value={newPage.description}
                    onChange={(e) => setNewPage({...newPage, description: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="页面描述"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      图标
                    </label>
                    <select
                      value={newPage.icon}
                      onChange={(e) => setNewPage({...newPage, icon: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {iconOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      类型
                    </label>
                    <input
                      type="text"
                      value={newPage.type}
                      onChange={(e) => setNewPage({...newPage, type: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="页面类型"
                    />
                  </div>
                </div>
                <div className="flex space-x-4">
                  <button
                    type="submit"
                    className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
                  >
                    添加页面
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
                  >
                    取消
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* 页面列表 */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">现有页面</h2>
            {Object.keys(pages).length === 0 ? (
              <p className="text-gray-600">暂无页面</p>
            ) : (
              <div className="grid gap-4">
                {Object.entries(pages).map(([pageId, pageInfo]) => (
                  <div key={pageId} className="border border-gray-200 rounded-lg p-4 flex justify-between items-center">
                    <div className="flex items-center">
                      <span className="material-icons-outlined text-blue-600 mr-3">
                        {pageInfo.icon}
                      </span>
                      <div>
                        <h3 className="font-semibold">{pageInfo.title}</h3>
                        <p className="text-sm text-gray-600">{pageInfo.description}</p>
                        <p className="text-xs text-gray-500">ID: {pageId} | 类型: {pageInfo.type}</p>
                      </div>
                    </div>
                    <button
                      onClick={() => handleDeletePage(pageId)}
                      className="bg-red-600 text-white px-3 py-1 rounded-md hover:bg-red-700 flex items-center"
                    >
                      <span className="material-icons-outlined mr-1 text-sm">delete</span>
                      删除
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  )
}
