'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'

interface StudentLessonsProps {
  studentId: string
}

export default function StudentLessons({ studentId }: StudentLessonsProps) {
  const [lessons, setLessons] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchLessons()
  }, [])

  const fetchLessons = async () => {
    try {
      const response = await fetch('/api/lessons')
      const data = await response.json()
      if (data.lessons) {
        // 只显示分配给当前学生的课程
        const studentLessons = data.lessons.filter((lesson: any) => 
          lesson.students.includes(studentId)
        )
        setLessons(studentLessons)
      }
    } catch (error) {
      console.error('获取课程列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="flex justify-center items-center h-64">
            <div className="text-lg text-gray-600">加载中...</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-6 flex items-center">
          <span className="material-icons-outlined text-blue-600 mr-3">library_books</span>
          我的课程
        </h1>
        
        {lessons.length === 0 ? (
          <div className="text-center py-12">
            <span className="material-icons-outlined text-gray-400 text-6xl mb-4">
              school
            </span>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              暂无课程
            </h3>
            <p className="text-gray-600">
              老师还没有为您分配课程，请耐心等待。
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {lessons.map((lesson) => (
              <div key={lesson.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">
                      {lesson.title}
                    </h2>
                    <p className="text-sm text-gray-600">
                      发布时间: {new Date(lesson.createdAt).toLocaleDateString('zh-CN')}
                    </p>
                  </div>
                  <Link
                    href={`/${studentId}/lesson-${lesson.id}`}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
                  >
                    <span className="material-icons-outlined mr-2">play_arrow</span>
                    开始学习
                  </Link>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div className="bg-red-50 p-4 rounded-lg">
                    <h3 className="font-medium text-red-800 mb-2 flex items-center">
                      <span className="material-icons-outlined text-red-600 mr-2">record_voice_over</span>
                      今日の発音
                    </h3>
                    <p className="text-sm text-red-700">
                      {lesson.content.pronunciation.items.length} 个发音练习
                    </p>
                    <div className="mt-2 space-y-1">
                      {lesson.content.pronunciation.items.slice(0, 2).map((item: any, index: number) => (
                        <p key={index} className="text-xs text-red-600">
                          • {item.word}
                        </p>
                      ))}
                      {lesson.content.pronunciation.items.length > 2 && (
                        <p className="text-xs text-red-500">
                          ...还有 {lesson.content.pronunciation.items.length - 2} 个
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="bg-green-50 p-4 rounded-lg">
                    <h3 className="font-medium text-green-800 mb-2 flex items-center">
                      <span className="material-icons-outlined text-green-600 mr-2">chat_bubble</span>
                      今日の表現
                    </h3>
                    <p className="text-sm text-green-700">
                      {lesson.content.expressions.items.length} 个表达练习
                    </p>
                    <div className="mt-2 space-y-1">
                      {lesson.content.expressions.items.slice(0, 2).map((item: any, index: number) => (
                        <p key={index} className="text-xs text-green-600">
                          • {item.expression}
                        </p>
                      ))}
                      {lesson.content.expressions.items.length > 2 && (
                        <p className="text-xs text-green-500">
                          ...还有 {lesson.content.expressions.items.length - 2} 个
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex space-x-4">
                    {lesson.homework && (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <span className="material-icons-outlined mr-1 text-xs">assignment</span>
                        有作业
                      </span>
                    )}
                    {lesson.notes && (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <span className="material-icons-outlined mr-1 text-xs">note</span>
                        有备注
                      </span>
                    )}
                  </div>
                  <div className="text-xs text-gray-500">
                    课程ID: {lesson.id}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="mt-8 bg-blue-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
            <span className="material-icons-outlined text-blue-600 mr-2">tips_and_updates</span>
            学习提示
          </h3>
          <ul className="space-y-2 text-gray-700 text-sm">
            <li>• 建议按照发布时间顺序学习课程</li>
            <li>• 每个课程都包含发音和表达两个部分</li>
            <li>• 完成课程后记得查看作业和备注</li>
            <li>• 有问题可以随时向老师询问</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
