import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 允许访问登录页面和API路由
  if (pathname === '/login' || pathname.startsWith('/api/')) {
    return NextResponse.next()
  }

  // 检查是否访问学生专属路径
  const studentPathMatch = pathname.match(/^\/([^\/]+)/)
  if (studentPathMatch) {
    const requestedStudent = studentPathMatch[1]
    
    // 获取JWT token
    const token = request.cookies.get('auth-token')?.value

    if (!token) {
      // 未登录，重定向到登录页
      return NextResponse.redirect(new URL('/login', request.url))
    }

    try {
      // 简化的token验证 - 在中间件中我们只检查token是否存在
      // 详细验证在API路由中进行
      const tokenParts = token.split('.')
      if (tokenParts.length !== 3) {
        throw new Error('Invalid token format')
      }

      // 解码payload (不验证签名，因为在Edge Runtime中有限制)
      const payload = JSON.parse(atob(tokenParts[1]))

      // 检查用户是否有权限访问该路径
      if (payload.userId !== requestedStudent) {
        // 无权限访问，返回404
        return NextResponse.rewrite(new URL('/404', request.url))
      }

      return NextResponse.next()
    } catch (error) {
      // Token无效，重定向到登录页
      return NextResponse.redirect(new URL('/login', request.url))
    }
  }

  // 根路径重定向到登录页
  if (pathname === '/') {
    return NextResponse.redirect(new URL('/login', request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
