import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 允许访问登录页面、API路由和教师页面
  if (pathname === '/login' || pathname.startsWith('/api/') || pathname === '/teacher') {
    return NextResponse.next()
  }

  // 检查是否访问学生专属路径
  const studentPathMatch = pathname.match(/^\/([^\/]+)/)
  if (studentPathMatch) {
    const requestedStudent = studentPathMatch[1]

    // 获取JWT token
    const token = request.cookies.get('auth-token')?.value

    if (!token) {
      // 未登录，重定向到登录页
      console.log('No token found, redirecting to login')
      return NextResponse.redirect(new URL('/login', request.url))
    }

    // 暂时简化验证 - 只要有token就允许访问
    // 详细的权限验证在页面组件中进行
    console.log('Token found, allowing access to:', pathname)
    return NextResponse.next()
  }

  // 根路径重定向到登录页
  if (pathname === '/') {
    return NextResponse.redirect(new URL('/login', request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
