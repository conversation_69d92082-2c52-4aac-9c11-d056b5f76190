import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 允许访问登录页面、API路由和教师页面
  if (pathname === '/login' || pathname.startsWith('/api/') || pathname === '/teacher') {
    return NextResponse.next()
  }

  // 检查是否访问学生专属路径
  const studentPathMatch = pathname.match(/^\/([^\/]+)/)
  if (studentPathMatch) {
    const requestedStudent = studentPathMatch[1]

    // 获取JWT token
    const token = request.cookies.get('auth-token')?.value

    if (!token) {
      // 未登录，重定向到登录页
      return NextResponse.redirect(new URL('/login', request.url))
    }

    // 验证学生是否存在
    try {
      const usersResponse = await fetch(new URL('/api/users/validate', request.url), {
        headers: {
          'Cookie': request.headers.get('cookie') || ''
        }
      })

      if (usersResponse.ok) {
        const data = await usersResponse.json()
        const validStudentIds = data.validStudentIds || []

        // 检查请求的学生ID是否有效
        if (!validStudentIds.includes(requestedStudent)) {
          // 学生不存在，返回404
          return NextResponse.rewrite(new URL('/404', request.url))
        }
      }
    } catch (error) {
      console.error('验证学生ID失败:', error)
      // 验证失败时重定向到登录页
      return NextResponse.redirect(new URL('/login', request.url))
    }

    return NextResponse.next()
  }

  // 根路径重定向到登录页
  if (pathname === '/') {
    return NextResponse.redirect(new URL('/login', request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
