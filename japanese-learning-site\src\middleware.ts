import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import jwt from 'jsonwebtoken'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 允许访问登录页面和API路由
  if (pathname === '/login' || pathname.startsWith('/api/')) {
    return NextResponse.next()
  }

  // 检查是否访问学生专属路径
  const studentPathMatch = pathname.match(/^\/([^\/]+)/)
  if (studentPathMatch) {
    const requestedStudent = studentPathMatch[1]
    
    // 获取JWT token
    const token = request.cookies.get('auth-token')?.value

    if (!token) {
      // 未登录，重定向到登录页
      return NextResponse.redirect(new URL('/login', request.url))
    }

    try {
      // 验证JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as { userId: string }
      
      // 检查用户是否有权限访问该路径
      if (decoded.userId !== requestedStudent) {
        // 无权限访问，返回404
        return NextResponse.rewrite(new URL('/404', request.url))
      }

      return NextResponse.next()
    } catch (error) {
      // Token无效，重定向到登录页
      return NextResponse.redirect(new URL('/login', request.url))
    }
  }

  // 根路径重定向到登录页
  if (pathname === '/') {
    return NextResponse.redirect(new URL('/login', request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
