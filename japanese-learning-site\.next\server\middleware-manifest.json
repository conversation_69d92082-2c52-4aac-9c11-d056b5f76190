{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "b3QeL9xh2/ZAhwjBNDih+/2vXqdKsg/jvq1/9lieYmE=", "__NEXT_PREVIEW_MODE_ID": "408e6d64556fc7e1121e82a7a2549a75", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "267ce559bb8b64fcb347aca4586cc1752c5b84ca8975237f7c5be448e61e99b8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6d9d7d7417770828bbc95a64ef424f05dca6f325f703a2515083a9f8ae44ca3a"}}}, "sortedMiddleware": ["/"], "functions": {}}