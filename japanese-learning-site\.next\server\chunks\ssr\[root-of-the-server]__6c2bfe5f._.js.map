{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/TeacherDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\ninterface PronunciationItem {\n  word: string\n  reading: string\n  meaning: string\n}\n\ninterface ExpressionItem {\n  expression: string\n  reading: string\n  meaning: string\n  usage: string\n  example: string\n}\n\nexport default function TeacherDashboard() {\n  const [activeTab, setActiveTab] = useState('publish') // publish, history, students\n  const [lessonTitle, setLessonTitle] = useState('')\n  const [selectedStudents, setSelectedStudents] = useState<string[]>([])\n  const [pronunciationItems, setPronunciationItems] = useState<PronunciationItem[]>([\n    { word: '', reading: '', meaning: '' }\n  ])\n  const [expressionItems, setExpressionItems] = useState<ExpressionItem[]>([\n    { expression: '', reading: '', meaning: '', usage: '', example: '' }\n  ])\n  const [homework, setHomework] = useState('')\n  const [notes, setNotes] = useState('')\n\n  // 数据状态\n  const [students, setStudents] = useState<any[]>([])\n  const [lessons, setLessons] = useState<any[]>([])\n  const [selectedStudent, setSelectedStudent] = useState('')\n\n  // 新学生表单\n  const [newStudentForm, setNewStudentForm] = useState({\n    username: '',\n    name: '',\n    password: ''\n  })\n  const [showAddStudentForm, setShowAddStudentForm] = useState(false)\n\n  // 获取数据\n  useEffect(() => {\n    fetchStudents()\n    fetchLessons()\n  }, [])\n\n  const fetchStudents = async () => {\n    try {\n      const response = await fetch('/api/students')\n      const data = await response.json()\n      if (data.students) {\n        setStudents(data.students)\n      }\n    } catch (error) {\n      console.error('获取学生列表失败:', error)\n    }\n  }\n\n  const fetchLessons = async () => {\n    try {\n      const response = await fetch('/api/lessons')\n      const data = await response.json()\n      if (data.lessons) {\n        setLessons(data.lessons)\n      }\n    } catch (error) {\n      console.error('获取课程列表失败:', error)\n    }\n  }\n\n  // 添加新学生\n  const handleAddStudent = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    const trimmedForm = {\n      username: newStudentForm.username.trim(),\n      name: newStudentForm.name.trim(),\n      password: newStudentForm.password.trim()\n    }\n\n    if (!trimmedForm.username || !trimmedForm.name || !trimmedForm.password) {\n      alert('所有字段都不能为空')\n      return\n    }\n\n    try {\n      const response = await fetch('/api/students', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(trimmedForm)\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        alert('学生添加成功！')\n        setNewStudentForm({ username: '', name: '', password: '' })\n        setShowAddStudentForm(false)\n        fetchStudents() // 刷新学生列表\n      } else {\n        alert(data.error || '添加学生失败')\n      }\n    } catch (error) {\n      console.error('添加学生失败:', error)\n      alert('添加学生失败')\n    }\n  }\n\n  const addPronunciationItem = () => {\n    setPronunciationItems([...pronunciationItems, { word: '', reading: '', meaning: '' }])\n  }\n\n  const addExpressionItem = () => {\n    setExpressionItems([...expressionItems, { expression: '', reading: '', meaning: '', usage: '', example: '' }])\n  }\n\n  const updatePronunciationItem = (index: number, field: keyof PronunciationItem, value: string) => {\n    const updated = [...pronunciationItems]\n    updated[index][field] = value\n    setPronunciationItems(updated)\n  }\n\n  const updateExpressionItem = (index: number, field: keyof ExpressionItem, value: string) => {\n    const updated = [...expressionItems]\n    updated[index][field] = value\n    setExpressionItems(updated)\n  }\n\n  const removePronunciationItem = (index: number) => {\n    setPronunciationItems(pronunciationItems.filter((_, i) => i !== index))\n  }\n\n  const removeExpressionItem = (index: number) => {\n    setExpressionItems(expressionItems.filter((_, i) => i !== index))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    const lessonData = {\n      title: lessonTitle,\n      students: selectedStudents,\n      content: {\n        pronunciation: {\n          title: \"今日の発音\",\n          items: pronunciationItems.filter(item => item.word.trim())\n        },\n        expressions: {\n          title: \"今日の表現\", \n          items: expressionItems.filter(item => item.expression.trim())\n        }\n      },\n      homework,\n      notes\n    }\n\n    try {\n      const response = await fetch('/api/lessons', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(lessonData)\n      })\n\n      if (response.ok) {\n        alert('课程发布成功！')\n        // 重置表单\n        setLessonTitle('')\n        setSelectedStudents([])\n        setPronunciationItems([{ word: '', reading: '', meaning: '' }])\n        setExpressionItems([{ expression: '', reading: '', meaning: '', usage: '', example: '' }])\n        setHomework('')\n        setNotes('')\n        fetchLessons() // 刷新课程列表\n      } else {\n        alert('发布失败，请重试')\n      }\n    } catch (error) {\n      console.error('发布错误:', error)\n      alert('发布失败，请重试')\n    }\n  }\n\n  // 过滤课程（按学生）\n  const filteredLessons = selectedStudent\n    ? lessons.filter(lesson => lesson.students.includes(selectedStudent))\n    : lessons\n\n  return (\n    <div className=\"max-w-7xl mx-auto\">\n      <div className=\"bg-white rounded-lg shadow-lg\">\n        {/* 标题 */}\n        <div className=\"border-b border-gray-200 px-8 py-6\">\n          <h1 className=\"text-3xl font-bold text-gray-900 flex items-center\">\n            <span className=\"material-icons-outlined text-blue-600 mr-3\">school</span>\n            教师控制台\n          </h1>\n        </div>\n\n        {/* 标签页导航 */}\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-8\">\n            <button\n              onClick={() => setActiveTab('publish')}\n              className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'publish'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <span className=\"material-icons-outlined mr-2 align-middle\">publish</span>\n              发布课程\n            </button>\n            <button\n              onClick={() => setActiveTab('history')}\n              className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'history'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <span className=\"material-icons-outlined mr-2 align-middle\">history</span>\n              历史记录\n            </button>\n            <button\n              onClick={() => setActiveTab('students')}\n              className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'students'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <span className=\"material-icons-outlined mr-2 align-middle\">people</span>\n              学生管理\n            </button>\n          </nav>\n        </div>\n\n        {/* 内容区域 */}\n        <div className=\"p-8\">{renderTabContent()}</div>\n      </div>\n    </div>\n  )\n\n  function renderTabContent() {\n    switch (activeTab) {\n      case 'publish':\n        return renderPublishTab()\n      case 'history':\n        return renderHistoryTab()\n      case 'students':\n        return renderStudentsTab()\n      default:\n        return renderPublishTab()\n    }\n  }\n\n  function renderPublishTab() {\n    return (\n\n        <div>\n          <h2 className=\"text-2xl font-semibold text-gray-900 mb-6\">发布新课程</h2>\n          <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {/* 基本信息 */}\n          <div className=\"bg-gray-50 p-6 rounded-lg\">\n            <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n              <span className=\"material-icons-outlined text-blue-600 mr-2\">info</span>\n              课程基本信息\n            </h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  课程标题\n                </label>\n                <input\n                  type=\"text\"\n                  value={lessonTitle}\n                  onChange={(e) => setLessonTitle(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"例：2024年1月15日 - 日常问候\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  发布给学生\n                </label>\n                <div className=\"space-y-2\">\n                  {students.map(student => (\n                    <label key={student.id} className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        checked={selectedStudents.includes(student.id)}\n                        onChange={(e) => {\n                          if (e.target.checked) {\n                            setSelectedStudents([...selectedStudents, student.id])\n                          } else {\n                            setSelectedStudents(selectedStudents.filter(id => id !== student.id))\n                          }\n                        }}\n                        className=\"mr-2\"\n                      />\n                      {student.name}\n                    </label>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* 发音练习 */}\n          <div className=\"bg-red-50 p-6 rounded-lg\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h2 className=\"text-xl font-semibold flex items-center\">\n                <span className=\"material-icons-outlined text-red-600 mr-2\">record_voice_over</span>\n                今日の発音\n              </h2>\n              <button\n                type=\"button\"\n                onClick={addPronunciationItem}\n                className=\"bg-red-600 text-white px-3 py-1 rounded-md hover:bg-red-700 flex items-center\"\n              >\n                <span className=\"material-icons-outlined mr-1 text-sm\">add</span>\n                添加\n              </button>\n            </div>\n            <div className=\"space-y-4\">\n              {pronunciationItems.map((item, index) => (\n                <div key={index} className=\"bg-white p-4 rounded border grid grid-cols-1 md:grid-cols-4 gap-3\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"日语单词\"\n                    value={item.word}\n                    onChange={(e) => updatePronunciationItem(index, 'word', e.target.value)}\n                    className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                  />\n                  <input\n                    type=\"text\"\n                    placeholder=\"读音 (romaji)\"\n                    value={item.reading}\n                    onChange={(e) => updatePronunciationItem(index, 'reading', e.target.value)}\n                    className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                  />\n                  <input\n                    type=\"text\"\n                    placeholder=\"中文意思\"\n                    value={item.meaning}\n                    onChange={(e) => updatePronunciationItem(index, 'meaning', e.target.value)}\n                    className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => removePronunciationItem(index)}\n                    className=\"bg-red-500 text-white px-3 py-2 rounded-md hover:bg-red-600\"\n                  >\n                    删除\n                  </button>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* 表达练习 */}\n          <div className=\"bg-green-50 p-6 rounded-lg\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h2 className=\"text-xl font-semibold flex items-center\">\n                <span className=\"material-icons-outlined text-green-600 mr-2\">chat_bubble</span>\n                今日の表現\n              </h2>\n              <button\n                type=\"button\"\n                onClick={addExpressionItem}\n                className=\"bg-green-600 text-white px-3 py-1 rounded-md hover:bg-green-700 flex items-center\"\n              >\n                <span className=\"material-icons-outlined mr-1 text-sm\">add</span>\n                添加\n              </button>\n            </div>\n            <div className=\"space-y-4\">\n              {expressionItems.map((item, index) => (\n                <div key={index} className=\"bg-white p-4 rounded border space-y-3\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n                    <input\n                      type=\"text\"\n                      placeholder=\"日语表达\"\n                      value={item.expression}\n                      onChange={(e) => updateExpressionItem(index, 'expression', e.target.value)}\n                      className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    />\n                    <input\n                      type=\"text\"\n                      placeholder=\"读音 (romaji)\"\n                      value={item.reading}\n                      onChange={(e) => updateExpressionItem(index, 'reading', e.target.value)}\n                      className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    />\n                    <input\n                      type=\"text\"\n                      placeholder=\"中文意思\"\n                      value={item.meaning}\n                      onChange={(e) => updateExpressionItem(index, 'meaning', e.target.value)}\n                      className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    />\n                  </div>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                    <input\n                      type=\"text\"\n                      placeholder=\"使用场合\"\n                      value={item.usage}\n                      onChange={(e) => updateExpressionItem(index, 'usage', e.target.value)}\n                      className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    />\n                    <input\n                      type=\"text\"\n                      placeholder=\"例句\"\n                      value={item.example}\n                      onChange={(e) => updateExpressionItem(index, 'example', e.target.value)}\n                      className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    />\n                  </div>\n                  <button\n                    type=\"button\"\n                    onClick={() => removeExpressionItem(index)}\n                    className=\"bg-red-500 text-white px-3 py-1 rounded-md hover:bg-red-600\"\n                  >\n                    删除\n                  </button>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* 作业和备注 */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"bg-yellow-50 p-6 rounded-lg\">\n              <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n                <span className=\"material-icons-outlined text-yellow-600 mr-2\">assignment</span>\n                宿題\n              </h2>\n              <textarea\n                value={homework}\n                onChange={(e) => setHomework(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500\"\n                rows={4}\n                placeholder=\"布置作业内容...\"\n              />\n            </div>\n            <div className=\"bg-blue-50 p-6 rounded-lg\">\n              <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n                <span className=\"material-icons-outlined text-blue-600 mr-2\">note</span>\n                注意事項\n              </h2>\n              <textarea\n                value={notes}\n                onChange={(e) => setNotes(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                rows={4}\n                placeholder=\"学习要点和注意事项...\"\n              />\n            </div>\n          </div>\n\n          {/* 提交按钮 */}\n          <div className=\"flex justify-center\">\n            <button\n              type=\"submit\"\n              className=\"bg-blue-600 text-white px-8 py-3 rounded-md hover:bg-blue-700 flex items-center text-lg font-medium\"\n            >\n              <span className=\"material-icons-outlined mr-2\">publish</span>\n              发布课程\n            </button>\n          </div>\n        </form>\n        </div>\n    )\n  }\n\n  function renderHistoryTab() {\n    return (\n      <div>\n        <div className=\"flex justify-between items-center mb-6\">\n          <h2 className=\"text-2xl font-semibold text-gray-900\">历史发布记录</h2>\n          <div className=\"flex items-center space-x-4\">\n            <select\n              value={selectedStudent}\n              onChange={(e) => setSelectedStudent(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">所有学生</option>\n              {students.map(student => (\n                <option key={student.id} value={student.id}>\n                  {student.name}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        {filteredLessons.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <span className=\"material-icons-outlined text-gray-400 text-6xl mb-4\">\n              history_edu\n            </span>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              暂无课程记录\n            </h3>\n            <p className=\"text-gray-600\">\n              {selectedStudent ? '该学生还没有课程记录' : '还没有发布任何课程'}\n            </p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {filteredLessons.map((lesson) => (\n              <div key={lesson.id} className=\"bg-gray-50 rounded-lg p-6 border border-gray-200\">\n                <div className=\"flex justify-between items-start mb-4\">\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900\">{lesson.title}</h3>\n                    <p className=\"text-sm text-gray-600\">\n                      发布时间: {new Date(lesson.createdAt).toLocaleString('zh-CN')}\n                    </p>\n                  </div>\n                  <div className=\"text-sm text-gray-500\">\n                    课程ID: {lesson.id}\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n                  <div>\n                    <h4 className=\"font-medium text-gray-900 mb-2 flex items-center\">\n                      <span className=\"material-icons-outlined text-red-600 mr-2\">record_voice_over</span>\n                      今日の発音 ({lesson.content.pronunciation.items.length}项)\n                    </h4>\n                    <div className=\"space-y-1\">\n                      {lesson.content.pronunciation.items.slice(0, 3).map((item: any, index: number) => (\n                        <p key={index} className=\"text-sm text-gray-700\">\n                          • {item.word} - {item.meaning}\n                        </p>\n                      ))}\n                      {lesson.content.pronunciation.items.length > 3 && (\n                        <p className=\"text-sm text-gray-500\">\n                          ...还有 {lesson.content.pronunciation.items.length - 3} 项\n                        </p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div>\n                    <h4 className=\"font-medium text-gray-900 mb-2 flex items-center\">\n                      <span className=\"material-icons-outlined text-green-600 mr-2\">chat_bubble</span>\n                      今日の表現 ({lesson.content.expressions.items.length}项)\n                    </h4>\n                    <div className=\"space-y-1\">\n                      {lesson.content.expressions.items.slice(0, 3).map((item: any, index: number) => (\n                        <p key={index} className=\"text-sm text-gray-700\">\n                          • {item.expression} - {item.meaning}\n                        </p>\n                      ))}\n                      {lesson.content.expressions.items.length > 3 && (\n                        <p className=\"text-sm text-gray-500\">\n                          ...还有 {lesson.content.expressions.items.length - 3} 项\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"border-t border-gray-200 pt-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4\">\n                      <span className=\"text-sm text-gray-600\">\n                        发布给: {lesson.students.map((studentId: string) => {\n                          const student = students.find(s => s.id === studentId)\n                          return student?.name || studentId\n                        }).join(', ')}\n                      </span>\n                    </div>\n                    <div className=\"flex space-x-2\">\n                      {lesson.homework && (\n                        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n                          <span className=\"material-icons-outlined mr-1 text-xs\">assignment</span>\n                          有作业\n                        </span>\n                      )}\n                      {lesson.notes && (\n                        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                          <span className=\"material-icons-outlined mr-1 text-xs\">note</span>\n                          有备注\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    )\n  }\n\n  function renderStudentsTab() {\n    return (\n      <div>\n        <div className=\"flex justify-between items-center mb-6\">\n          <h2 className=\"text-2xl font-semibold text-gray-900\">学生管理</h2>\n          <button\n            onClick={() => setShowAddStudentForm(true)}\n            className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center\"\n          >\n            <span className=\"material-icons-outlined mr-2\">person_add</span>\n            添加学生\n          </button>\n        </div>\n\n        {/* 添加学生表单 */}\n        {showAddStudentForm && (\n          <div className=\"mb-8 bg-blue-50 p-6 rounded-lg border border-blue-200\">\n            <h3 className=\"text-lg font-semibold mb-4\">添加新学生</h3>\n            <form onSubmit={handleAddStudent} className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    用户名\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={newStudentForm.username}\n                    onChange={(e) => setNewStudentForm({...newStudentForm, username: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"学生登录用户名\"\n                    required\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    姓名\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={newStudentForm.name}\n                    onChange={(e) => setNewStudentForm({...newStudentForm, name: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"学生真实姓名\"\n                    required\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    初始密码\n                  </label>\n                  <input\n                    type=\"password\"\n                    value={newStudentForm.password}\n                    onChange={(e) => setNewStudentForm({...newStudentForm, password: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"设置初始密码\"\n                    required\n                  />\n                </div>\n              </div>\n              <div className=\"flex space-x-4\">\n                <button\n                  type=\"submit\"\n                  className=\"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700\"\n                >\n                  添加学生\n                </button>\n                <button\n                  type=\"button\"\n                  onClick={() => setShowAddStudentForm(false)}\n                  className=\"bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700\"\n                >\n                  取消\n                </button>\n              </div>\n            </form>\n          </div>\n        )}\n\n        {/* 学生列表 */}\n        <div className=\"grid gap-4\">\n          {students.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <span className=\"material-icons-outlined text-gray-400 text-6xl mb-4\">\n                people_outline\n              </span>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                暂无学生\n              </h3>\n              <p className=\"text-gray-600\">\n                点击上方按钮添加第一个学生\n              </p>\n            </div>\n          ) : (\n            students.map((student) => {\n              const studentLessons = lessons.filter(lesson => lesson.students.includes(student.id))\n              return (\n                <div key={student.id} className=\"bg-gray-50 rounded-lg p-6 border border-gray-200\">\n                  <div className=\"flex justify-between items-start\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900\">{student.name}</h3>\n                      <p className=\"text-sm text-gray-600\">用户名: {student.username}</p>\n                      <p className=\"text-sm text-gray-600\">学生ID: {student.id}</p>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-sm text-gray-600\">\n                        已发布课程: {studentLessons.length} 个\n                      </div>\n                      <div className=\"text-xs text-gray-500 mt-1\">\n                        最近课程: {studentLessons.length > 0\n                          ? new Date(studentLessons[0].createdAt).toLocaleDateString('zh-CN')\n                          : '无'\n                        }\n                      </div>\n                    </div>\n                  </div>\n\n                  {studentLessons.length > 0 && (\n                    <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                      <h4 className=\"text-sm font-medium text-gray-900 mb-2\">最近课程:</h4>\n                      <div className=\"space-y-1\">\n                        {studentLessons.slice(0, 3).map((lesson) => (\n                          <div key={lesson.id} className=\"text-sm text-gray-700 flex justify-between\">\n                            <span>{lesson.title}</span>\n                            <span className=\"text-gray-500\">\n                              {new Date(lesson.createdAt).toLocaleDateString('zh-CN')}\n                            </span>\n                          </div>\n                        ))}\n                        {studentLessons.length > 3 && (\n                          <p className=\"text-xs text-gray-500\">\n                            ...还有 {studentLessons.length - 3} 个课程\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              )\n            })\n          )}\n        </div>\n      </div>\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAkBe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,6BAA6B;;IACnF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QAChF;YAAE,MAAM;YAAI,SAAS;YAAI,SAAS;QAAG;KACtC;IACD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACvE;YAAE,YAAY;YAAI,SAAS;YAAI,SAAS;YAAI,OAAO;YAAI,SAAS;QAAG;KACpE;IACD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,OAAO;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAChD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,QAAQ;IACR,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,UAAU;QACV,MAAM;QACN,UAAU;IACZ;IACA,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,OAAO;IACP,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,QAAQ,EAAE;gBACjB,YAAY,KAAK,QAAQ;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW,KAAK,OAAO;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,QAAQ;IACR,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAEhB,MAAM,cAAc;YAClB,UAAU,eAAe,QAAQ,CAAC,IAAI;YACtC,MAAM,eAAe,IAAI,CAAC,IAAI;YAC9B,UAAU,eAAe,QAAQ,CAAC,IAAI;QACxC;QAEA,IAAI,CAAC,YAAY,QAAQ,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,QAAQ,EAAE;YACvE,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN,kBAAkB;oBAAE,UAAU;oBAAI,MAAM;oBAAI,UAAU;gBAAG;gBACzD,sBAAsB;gBACtB,gBAAgB,SAAS;;YAC3B,OAAO;gBACL,MAAM,KAAK,KAAK,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR;IACF;IAEA,MAAM,uBAAuB;QAC3B,sBAAsB;eAAI;YAAoB;gBAAE,MAAM;gBAAI,SAAS;gBAAI,SAAS;YAAG;SAAE;IACvF;IAEA,MAAM,oBAAoB;QACxB,mBAAmB;eAAI;YAAiB;gBAAE,YAAY;gBAAI,SAAS;gBAAI,SAAS;gBAAI,OAAO;gBAAI,SAAS;YAAG;SAAE;IAC/G;IAEA,MAAM,0BAA0B,CAAC,OAAe,OAAgC;QAC9E,MAAM,UAAU;eAAI;SAAmB;QACvC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG;QACxB,sBAAsB;IACxB;IAEA,MAAM,uBAAuB,CAAC,OAAe,OAA6B;QACxE,MAAM,UAAU;eAAI;SAAgB;QACpC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG;QACxB,mBAAmB;IACrB;IAEA,MAAM,0BAA0B,CAAC;QAC/B,sBAAsB,mBAAmB,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IAClE;IAEA,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB,gBAAgB,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IAC5D;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,MAAM,aAAa;YACjB,OAAO;YACP,UAAU;YACV,SAAS;gBACP,eAAe;oBACb,OAAO;oBACP,OAAO,mBAAmB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,IAAI;gBACzD;gBACA,aAAa;oBACX,OAAO;oBACP,OAAO,gBAAgB,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC,IAAI;gBAC5D;YACF;YACA;YACA;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,OAAO;gBACP,eAAe;gBACf,oBAAoB,EAAE;gBACtB,sBAAsB;oBAAC;wBAAE,MAAM;wBAAI,SAAS;wBAAI,SAAS;oBAAG;iBAAE;gBAC9D,mBAAmB;oBAAC;wBAAE,YAAY;wBAAI,SAAS;wBAAI,SAAS;wBAAI,OAAO;wBAAI,SAAS;oBAAG;iBAAE;gBACzF,YAAY;gBACZ,SAAS;gBACT,eAAe,SAAS;;YAC1B,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM;QACR;IACF;IAEA,YAAY;IACZ,MAAM,kBAAkB,kBACpB,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,CAAC,QAAQ,CAAC,oBAClD;IAEJ,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAK,WAAU;0CAA6C;;;;;;4BAAa;;;;;;;;;;;;8BAM9E,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,YACV,kCACA,8EACJ;;kDAEF,8OAAC;wCAAK,WAAU;kDAA4C;;;;;;oCAAc;;;;;;;0CAG5E,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,YACV,kCACA,8EACJ;;kDAEF,8OAAC;wCAAK,WAAU;kDAA4C;;;;;;oCAAc;;;;;;;0CAG5E,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,kCACA,8EACJ;;kDAEF,8OAAC;wCAAK,WAAU;kDAA4C;;;;;;oCAAa;;;;;;;;;;;;;;;;;;8BAO/E,8OAAC;oBAAI,WAAU;8BAAO;;;;;;;;;;;;;;;;;;IAK5B,SAAS;QACP,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,SAAS;QACP,qBAEI,8OAAC;;8BACC,8OAAC;oBAAG,WAAU;8BAA4C;;;;;;8BAC1D,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAExC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAA6C;;;;;;wCAAW;;;;;;;8CAG1E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAGZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDAAI,WAAU;8DACZ,SAAS,GAAG,CAAC,CAAA,wBACZ,8OAAC;4DAAuB,WAAU;;8EAChC,8OAAC;oEACC,MAAK;oEACL,SAAS,iBAAiB,QAAQ,CAAC,QAAQ,EAAE;oEAC7C,UAAU,CAAC;wEACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;4EACpB,oBAAoB;mFAAI;gFAAkB,QAAQ,EAAE;6EAAC;wEACvD,OAAO;4EACL,oBAAoB,iBAAiB,MAAM,CAAC,CAAA,KAAM,OAAO,QAAQ,EAAE;wEACrE;oEACF;oEACA,WAAU;;;;;;gEAEX,QAAQ,IAAI;;2DAbH,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAsBhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;gDAAwB;;;;;;;sDAGtF,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAuC;;;;;;gDAAU;;;;;;;;;;;;;8CAIrE,8OAAC;oCAAI,WAAU;8CACZ,mBAAmB,GAAG,CAAC,CAAC,MAAM,sBAC7B,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,KAAK,IAAI;oDAChB,UAAU,CAAC,IAAM,wBAAwB,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACtE,WAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,KAAK,OAAO;oDACnB,UAAU,CAAC,IAAM,wBAAwB,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;oDACzE,WAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,KAAK,OAAO;oDACnB,UAAU,CAAC,IAAM,wBAAwB,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;oDACzE,WAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,wBAAwB;oDACvC,WAAU;8DACX;;;;;;;2CA1BO;;;;;;;;;;;;;;;;sCAmChB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA8C;;;;;;gDAAkB;;;;;;;sDAGlF,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAuC;;;;;;gDAAU;;;;;;;;;;;;;8CAIrE,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,KAAK,UAAU;4DACtB,UAAU,CAAC,IAAM,qBAAqB,OAAO,cAAc,EAAE,MAAM,CAAC,KAAK;4DACzE,WAAU;;;;;;sEAEZ,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,KAAK,OAAO;4DACnB,UAAU,CAAC,IAAM,qBAAqB,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;4DACtE,WAAU;;;;;;sEAEZ,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,KAAK,OAAO;4DACnB,UAAU,CAAC,IAAM,qBAAqB,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;4DACtE,WAAU;;;;;;;;;;;;8DAGd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,KAAK,KAAK;4DACjB,UAAU,CAAC,IAAM,qBAAqB,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK;4DACpE,WAAU;;;;;;sEAEZ,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,KAAK,OAAO;4DACnB,UAAU,CAAC,IAAM,qBAAqB,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;4DACtE,WAAU;;;;;;;;;;;;8DAGd,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,qBAAqB;oDACpC,WAAU;8DACX;;;;;;;2CA5CO;;;;;;;;;;;;;;;;sCAqDhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA+C;;;;;;gDAAiB;;;;;;;sDAGlF,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;4CACV,MAAM;4CACN,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6C;;;;;;gDAAW;;;;;;;sDAG1E,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,WAAU;4CACV,MAAM;4CACN,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;oCAAc;;;;;;;;;;;;;;;;;;;;;;;;IAOzE;IAEA,SAAS;QACP,qBACE,8OAAC;;8BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,SAAS,GAAG,CAAC,CAAA,wBACZ,8OAAC;4CAAwB,OAAO,QAAQ,EAAE;sDACvC,QAAQ,IAAI;2CADF,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;gBAQ9B,gBAAgB,MAAM,KAAK,kBAC1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAsD;;;;;;sCAGtE,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,8OAAC;4BAAE,WAAU;sCACV,kBAAkB,eAAe;;;;;;;;;;;yCAItC,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;4BAAoB,WAAU;;8CAC7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAuC,OAAO,KAAK;;;;;;8DACjE,8OAAC;oDAAE,WAAU;;wDAAwB;wDAC5B,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;;sDAGrD,8OAAC;4CAAI,WAAU;;gDAAwB;gDAC9B,OAAO,EAAE;;;;;;;;;;;;;8CAIpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;sEAA4C;;;;;;wDAAwB;wDAC5E,OAAO,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM;wDAAC;;;;;;;8DAEpD,8OAAC;oDAAI,WAAU;;wDACZ,OAAO,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAW,sBAC9D,8OAAC;gEAAc,WAAU;;oEAAwB;oEAC5C,KAAK,IAAI;oEAAC;oEAAI,KAAK,OAAO;;+DADvB;;;;;wDAIT,OAAO,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,mBAC3C,8OAAC;4DAAE,WAAU;;gEAAwB;gEAC5B,OAAO,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;gEAAE;;;;;;;;;;;;;;;;;;;sDAM7D,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;sEAA8C;;;;;;wDAAkB;wDACxE,OAAO,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM;wDAAC;;;;;;;8DAElD,8OAAC;oDAAI,WAAU;;wDACZ,OAAO,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAW,sBAC5D,8OAAC;gEAAc,WAAU;;oEAAwB;oEAC5C,KAAK,UAAU;oEAAC;oEAAI,KAAK,OAAO;;+DAD7B;;;;;wDAIT,OAAO,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,mBACzC,8OAAC;4DAAE,WAAU;;gEAAwB;gEAC5B,OAAO,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG;gEAAE;;;;;;;;;;;;;;;;;;;;;;;;;8CAO7D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;wDAAwB;wDAChC,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;4DACzB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;4DAC5C,OAAO,SAAS,QAAQ;wDAC1B,GAAG,IAAI,CAAC;;;;;;;;;;;;0DAGZ,8OAAC;gDAAI,WAAU;;oDACZ,OAAO,QAAQ,kBACd,8OAAC;wDAAK,WAAU;;0EACd,8OAAC;gEAAK,WAAU;0EAAuC;;;;;;4DAAiB;;;;;;;oDAI3E,OAAO,KAAK,kBACX,8OAAC;wDAAK,WAAU;;0EACd,8OAAC;gEAAK,WAAU;0EAAuC;;;;;;4DAAW;;;;;;;;;;;;;;;;;;;;;;;;;2BAxEpE,OAAO,EAAE;;;;;;;;;;;;;;;;IAqF/B;IAEA,SAAS;QACP,qBACE,8OAAC;;8BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,8OAAC;4BACC,SAAS,IAAM,sBAAsB;4BACrC,WAAU;;8CAEV,8OAAC;oCAAK,WAAU;8CAA+B;;;;;;gCAAiB;;;;;;;;;;;;;gBAMnE,oCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAK,UAAU;4BAAkB,WAAU;;8CAC1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,eAAe,QAAQ;oDAC9B,UAAU,CAAC,IAAM,kBAAkB;4DAAC,GAAG,cAAc;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAA;oDAC/E,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAGZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,eAAe,IAAI;oDAC1B,UAAU,CAAC,IAAM,kBAAkB;4DAAC,GAAG,cAAc;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAA;oDAC3E,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAGZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,eAAe,QAAQ;oDAC9B,UAAU,CAAC,IAAM,kBAAkB;4DAAC,GAAG,cAAc;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAA;oDAC/E,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,sBAAsB;4CACrC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;8BAST,8OAAC;oBAAI,WAAU;8BACZ,SAAS,MAAM,KAAK,kBACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAsD;;;;;;0CAGtE,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;+BAK/B,SAAS,GAAG,CAAC,CAAC;wBACZ,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE;wBACnF,qBACE,8OAAC;4BAAqB,WAAU;;8CAC9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAuC,QAAQ,IAAI;;;;;;8DACjE,8OAAC;oDAAE,WAAU;;wDAAwB;wDAAM,QAAQ,QAAQ;;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;;wDAAwB;wDAAO,QAAQ,EAAE;;;;;;;;;;;;;sDAExD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDAAwB;wDAC7B,eAAe,MAAM;wDAAC;;;;;;;8DAEhC,8OAAC;oDAAI,WAAU;;wDAA6B;wDACnC,eAAe,MAAM,GAAG,IAC3B,IAAI,KAAK,cAAc,CAAC,EAAE,CAAC,SAAS,EAAE,kBAAkB,CAAC,WACzD;;;;;;;;;;;;;;;;;;;gCAMT,eAAe,MAAM,GAAG,mBACvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;;gDACZ,eAAe,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,uBAC/B,8OAAC;wDAAoB,WAAU;;0EAC7B,8OAAC;0EAAM,OAAO,KAAK;;;;;;0EACnB,8OAAC;gEAAK,WAAU;0EACb,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB,CAAC;;;;;;;uDAHzC,OAAO,EAAE;;;;;gDAOpB,eAAe,MAAM,GAAG,mBACvB,8OAAC;oDAAE,WAAU;;wDAAwB;wDAC5B,eAAe,MAAM,GAAG;wDAAE;;;;;;;;;;;;;;;;;;;;2BAlCnC,QAAQ,EAAE;;;;;oBA0CxB;;;;;;;;;;;;IAKV;AACF", "debugId": null}}]}