{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/Layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\n\ninterface LayoutProps {\n  children: React.ReactNode\n  studentId: string\n  currentPage?: string\n  pages: Record<string, {\n    title: string\n    description: string\n    icon: string\n    type: string\n  }>\n}\n\nexport default function Layout({ children, studentId, currentPage, pages }: LayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const router = useRouter()\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' })\n      router.push('/login')\n    } catch (error) {\n      console.error('登出失败:', error)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 头部导航 */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => setSidebarOpen(!sidebarOpen)}\n                className=\"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\"\n              >\n                <span className=\"material-icons-outlined\">menu</span>\n              </button>\n              <h1 className=\"ml-2 text-xl font-semibold text-gray-900\">\n                日语学习网站\n              </h1>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-600\">欢迎，{studentId}</span>\n              <button\n                onClick={handleLogout}\n                className=\"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md\"\n              >\n                <span className=\"material-icons-outlined mr-1 text-sm\">logout</span>\n                登出\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"flex\">\n        {/* 侧边栏 */}\n        <aside className={`${\n          sidebarOpen ? 'translate-x-0' : '-translate-x-full'\n        } md:translate-x-0 fixed md:static inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-200 ease-in-out`}>\n          <div className=\"flex flex-col h-full\">\n            <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n              <div className=\"flex items-center flex-shrink-0 px-4\">\n                <span className=\"material-icons-outlined text-blue-600 mr-2\">person</span>\n                <span className=\"text-lg font-medium text-gray-900\">{studentId} 的学习空间</span>\n              </div>\n              \n              <nav className=\"mt-8 flex-1 px-2 space-y-1\">\n                {Object.entries(pages).map(([pageId, pageInfo]) => (\n                  <Link\n                    key={pageId}\n                    href={`/${studentId}/${pageId}`}\n                    className={`${\n                      currentPage === pageId\n                        ? 'bg-blue-100 text-blue-900 border-r-2 border-blue-600'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    } group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors`}\n                  >\n                    <span className=\"material-icons-outlined mr-3 text-lg\">\n                      {pageInfo.icon}\n                    </span>\n                    <div>\n                      <div className=\"font-medium\">{pageInfo.title}</div>\n                      <div className=\"text-xs text-gray-500\">{pageInfo.description}</div>\n                    </div>\n                  </Link>\n                ))}\n              </nav>\n            </div>\n          </div>\n        </aside>\n\n        {/* 遮罩层 (移动端) */}\n        {sidebarOpen && (\n          <div\n            className=\"md:hidden fixed inset-0 z-40 bg-gray-600 bg-opacity-75\"\n            onClick={() => setSidebarOpen(false)}\n          />\n        )}\n\n        {/* 主内容区域 */}\n        <main className=\"flex-1 md:ml-0\">\n          <div className=\"py-6 px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAkBe,SAAS,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,EAAe;IACrF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;YACjD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDAA0B;;;;;;;;;;;kDAE5C,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;;;;;;;0CAK3D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAwB;4CAAI;;;;;;;kDAC5C,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAuC;;;;;;4CAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9E,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAM,WAAW,GAChB,cAAc,kBAAkB,oBACjC,uIAAuI,CAAC;kCACvI,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA6C;;;;;;0DAC7D,8OAAC;gDAAK,WAAU;;oDAAqC;oDAAU;;;;;;;;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,QAAQ,SAAS,iBAC5C,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,QAAQ;gDAC/B,WAAW,GACT,gBAAgB,SACZ,yDACA,qDACL,mFAAmF,CAAC;;kEAErF,8OAAC;wDAAK,WAAU;kEACb,SAAS,IAAI;;;;;;kEAEhB,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAAe,SAAS,KAAK;;;;;;0EAC5C,8OAAC;gEAAI,WAAU;0EAAyB,SAAS,WAAW;;;;;;;;;;;;;+CAbzD;;;;;;;;;;;;;;;;;;;;;;;;;;oBAuBhB,6BACC,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,eAAe;;;;;;kCAKlC,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/StudentLessons.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\n\ninterface StudentLessonsProps {\n  studentId: string\n}\n\nexport default function StudentLessons({ studentId }: StudentLessonsProps) {\n  const [lessons, setLessons] = useState<any[]>([])\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    fetchLessons()\n  }, [])\n\n  const fetchLessons = async () => {\n    try {\n      const response = await fetch('/api/lessons')\n      const data = await response.json()\n      if (data.lessons) {\n        // 只显示分配给当前学生的课程\n        const studentLessons = data.lessons.filter((lesson: any) => \n          lesson.students.includes(studentId)\n        )\n        setLessons(studentLessons)\n      }\n    } catch (error) {\n      console.error('获取课程列表失败:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          <div className=\"flex justify-center items-center h-64\">\n            <div className=\"text-lg text-gray-600\">加载中...</div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-6 flex items-center\">\n          <span className=\"material-icons-outlined text-blue-600 mr-3\">library_books</span>\n          我的课程\n        </h1>\n        \n        {lessons.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <span className=\"material-icons-outlined text-gray-400 text-6xl mb-4\">\n              school\n            </span>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              暂无课程\n            </h3>\n            <p className=\"text-gray-600\">\n              老师还没有为您分配课程，请耐心等待。\n            </p>\n          </div>\n        ) : (\n          <div className=\"space-y-6\">\n            {lessons.map((lesson) => (\n              <div key={lesson.id} className=\"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow\">\n                <div className=\"flex justify-between items-start mb-4\">\n                  <div>\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                      {lesson.title}\n                    </h2>\n                    <p className=\"text-sm text-gray-600\">\n                      发布时间: {new Date(lesson.createdAt).toLocaleDateString('zh-CN')}\n                    </p>\n                  </div>\n                  <Link\n                    href={`/${studentId}/lesson-${lesson.id}`}\n                    className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center\"\n                  >\n                    <span className=\"material-icons-outlined mr-2\">play_arrow</span>\n                    开始学习\n                  </Link>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n                  <div className=\"bg-red-50 p-4 rounded-lg\">\n                    <h3 className=\"font-medium text-red-800 mb-2 flex items-center\">\n                      <span className=\"material-icons-outlined text-red-600 mr-2\">record_voice_over</span>\n                      今日の発音\n                    </h3>\n                    <p className=\"text-sm text-red-700\">\n                      {lesson.content.pronunciation.items.length} 个发音练习\n                    </p>\n                    <div className=\"mt-2 space-y-1\">\n                      {lesson.content.pronunciation.items.slice(0, 2).map((item: any, index: number) => (\n                        <p key={index} className=\"text-xs text-red-600\">\n                          • {item.word}\n                        </p>\n                      ))}\n                      {lesson.content.pronunciation.items.length > 2 && (\n                        <p className=\"text-xs text-red-500\">\n                          ...还有 {lesson.content.pronunciation.items.length - 2} 个\n                        </p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"bg-green-50 p-4 rounded-lg\">\n                    <h3 className=\"font-medium text-green-800 mb-2 flex items-center\">\n                      <span className=\"material-icons-outlined text-green-600 mr-2\">chat_bubble</span>\n                      今日の表現\n                    </h3>\n                    <p className=\"text-sm text-green-700\">\n                      {lesson.content.expressions.items.length} 个表达练习\n                    </p>\n                    <div className=\"mt-2 space-y-1\">\n                      {lesson.content.expressions.items.slice(0, 2).map((item: any, index: number) => (\n                        <p key={index} className=\"text-xs text-green-600\">\n                          • {item.expression}\n                        </p>\n                      ))}\n                      {lesson.content.expressions.items.length > 2 && (\n                        <p className=\"text-xs text-green-500\">\n                          ...还有 {lesson.content.expressions.items.length - 2} 个\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center justify-between pt-4 border-t border-gray-200\">\n                  <div className=\"flex space-x-4\">\n                    {lesson.homework && (\n                      <span className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n                        <span className=\"material-icons-outlined mr-1 text-xs\">assignment</span>\n                        有作业\n                      </span>\n                    )}\n                    {lesson.notes && (\n                      <span className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                        <span className=\"material-icons-outlined mr-1 text-xs\">note</span>\n                        有备注\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    课程ID: {lesson.id}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n\n        <div className=\"mt-8 bg-blue-50 p-6 rounded-lg\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-3 flex items-center\">\n            <span className=\"material-icons-outlined text-blue-600 mr-2\">tips_and_updates</span>\n            学习提示\n          </h3>\n          <ul className=\"space-y-2 text-gray-700 text-sm\">\n            <li>• 建议按照发布时间顺序学习课程</li>\n            <li>• 每个课程都包含发音和表达两个部分</li>\n            <li>• 完成课程后记得查看作业和备注</li>\n            <li>• 有问题可以随时向老师询问</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASe,SAAS,eAAe,EAAE,SAAS,EAAuB;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB;gBAChB,MAAM,iBAAiB,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,SAC1C,OAAO,QAAQ,CAAC,QAAQ,CAAC;gBAE3B,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;IAKjD;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAK,WAAU;sCAA6C;;;;;;wBAAoB;;;;;;;gBAIlF,QAAQ,MAAM,KAAK,kBAClB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAsD;;;;;;sCAGtE,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;yCAK/B,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;4BAAoB,WAAU;;8CAC7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,OAAO,KAAK;;;;;;8DAEf,8OAAC;oDAAE,WAAU;;wDAAwB;wDAC5B,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;sDAGzD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,UAAU,QAAQ,EAAE,OAAO,EAAE,EAAE;4CACzC,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;gDAAiB;;;;;;;;;;;;;8CAKpE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;sEAA4C;;;;;;wDAAwB;;;;;;;8DAGtF,8OAAC;oDAAE,WAAU;;wDACV,OAAO,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM;wDAAC;;;;;;;8DAE7C,8OAAC;oDAAI,WAAU;;wDACZ,OAAO,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAW,sBAC9D,8OAAC;gEAAc,WAAU;;oEAAuB;oEAC3C,KAAK,IAAI;;+DADN;;;;;wDAIT,OAAO,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,mBAC3C,8OAAC;4DAAE,WAAU;;gEAAuB;gEAC3B,OAAO,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;gEAAE;;;;;;;;;;;;;;;;;;;sDAM7D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;sEAA8C;;;;;;wDAAkB;;;;;;;8DAGlF,8OAAC;oDAAE,WAAU;;wDACV,OAAO,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM;wDAAC;;;;;;;8DAE3C,8OAAC;oDAAI,WAAU;;wDACZ,OAAO,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAW,sBAC5D,8OAAC;gEAAc,WAAU;;oEAAyB;oEAC7C,KAAK,UAAU;;+DADZ;;;;;wDAIT,OAAO,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,mBACzC,8OAAC;4DAAE,WAAU;;gEAAyB;gEAC7B,OAAO,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG;gEAAE;;;;;;;;;;;;;;;;;;;;;;;;;8CAO7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,OAAO,QAAQ,kBACd,8OAAC;oDAAK,WAAU;;sEACd,8OAAC;4DAAK,WAAU;sEAAuC;;;;;;wDAAiB;;;;;;;gDAI3E,OAAO,KAAK,kBACX,8OAAC;oDAAK,WAAU;;sEACd,8OAAC;4DAAK,WAAU;sEAAuC;;;;;;wDAAW;;;;;;;;;;;;;sDAKxE,8OAAC;4CAAI,WAAU;;gDAAwB;gDAC9B,OAAO,EAAE;;;;;;;;;;;;;;2BAjFZ,OAAO,EAAE;;;;;;;;;;8BAyFzB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAA6C;;;;;;gCAAuB;;;;;;;sCAGtF,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}]}