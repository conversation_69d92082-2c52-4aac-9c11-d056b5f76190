import { getUserPages } from '@/lib/auth'
import Layout from '@/components/Layout'
import StudentLessons from '@/components/StudentLessons'
import Link from 'next/link'

interface StudentPageProps {
  params: Promise<{ student: string }>
}

export default async function StudentPage({ params }: StudentPageProps) {
  const { student } = await params
  const pages = getUserPages(student)

  return (
    <Layout studentId={student} pages={pages}>
      <div className="space-y-8">
        {/* 课程列表 */}
        <StudentLessons studentId={student} />

        {/* 其他学习资源 */}
        {Object.keys(pages).length > 0 && (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <span className="material-icons-outlined text-green-600 mr-3">library_books</span>
                其他学习资源
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Object.entries(pages).map(([pageId, pageInfo]) => (
                  <Link
                    key={pageId}
                    href={`/${student}/${pageId}`}
                    className="block bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg p-6 hover:shadow-lg transition-all duration-200 hover:scale-105"
                  >
                    <div className="flex items-center mb-4">
                      <span className="material-icons-outlined text-green-600 text-3xl mr-3">
                        {pageInfo.icon}
                      </span>
                      <h3 className="text-xl font-semibold text-gray-900">
                        {pageInfo.title}
                      </h3>
                    </div>
                    <p className="text-gray-600 mb-4">
                      {pageInfo.description}
                    </p>
                    <div className="flex items-center text-green-600 font-medium">
                      <span>开始学习</span>
                      <span className="material-icons-outlined ml-1">arrow_forward</span>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  )
}
