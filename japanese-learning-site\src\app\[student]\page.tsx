import { getUserPages } from '@/lib/auth'
import Layout from '@/components/Layout'
import Link from 'next/link'

interface StudentPageProps {
  params: Promise<{ student: string }>
}

export default async function StudentPage({ params }: StudentPageProps) {
  const { student } = await params
  const pages = getUserPages(student)

  return (
    <Layout studentId={student} pages={pages}>
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6 flex items-center">
            <span className="material-icons-outlined text-blue-600 mr-3">dashboard</span>
            学习中心
          </h1>
          
          <p className="text-gray-600 mb-8">
            欢迎来到您的个人学习空间！选择下面的学习内容开始您的日语学习之旅。
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Object.entries(pages).map(([pageId, pageInfo]) => (
              <Link
                key={pageId}
                href={`/${student}/${pageId}`}
                className="block bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-6 hover:shadow-lg transition-all duration-200 hover:scale-105"
              >
                <div className="flex items-center mb-4">
                  <span className="material-icons-outlined text-blue-600 text-3xl mr-3">
                    {pageInfo.icon}
                  </span>
                  <h3 className="text-xl font-semibold text-gray-900">
                    {pageInfo.title}
                  </h3>
                </div>
                <p className="text-gray-600 mb-4">
                  {pageInfo.description}
                </p>
                <div className="flex items-center text-blue-600 font-medium">
                  <span>开始学习</span>
                  <span className="material-icons-outlined ml-1">arrow_forward</span>
                </div>
              </Link>
            ))}
          </div>

          {Object.keys(pages).length === 0 && (
            <div className="text-center py-12">
              <span className="material-icons-outlined text-gray-400 text-6xl mb-4">
                library_books
              </span>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                暂无学习内容
              </h3>
              <p className="text-gray-600">
                请联系管理员为您添加学习材料。
              </p>
            </div>
          )}
        </div>
      </div>
    </Layout>
  )
}
