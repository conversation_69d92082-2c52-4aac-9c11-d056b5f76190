export default function ListeningPractice() {
  const exercises = [
    {
      title: "日常对话",
      description: "两人在咖啡店的对话",
      questions: [
        "他们点了什么饮料？",
        "约定的时间是什么时候？",
        "他们讨论了什么话题？"
      ]
    },
    {
      title: "天气预报",
      description: "明天的天气预报",
      questions: [
        "明天的最高温度是多少？",
        "会下雨吗？",
        "适合外出吗？"
      ]
    }
  ]

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-6 flex items-center">
          <span className="material-icons-outlined text-blue-600 mr-3">headphones</span>
          听力练习
        </h1>
        
        <div className="space-y-8">
          {exercises.map((exercise, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">
                  练习 {index + 1}: {exercise.title}
                </h2>
                <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center">
                  <span className="material-icons-outlined mr-2">play_arrow</span>
                  播放
                </button>
              </div>
              
              <p className="text-gray-600 mb-4">{exercise.description}</p>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium text-gray-900 mb-3">听力问题：</h3>
                <ol className="space-y-2">
                  {exercise.questions.map((question, qIndex) => (
                    <li key={qIndex} className="flex items-start">
                      <span className="text-blue-600 font-medium mr-2">{qIndex + 1}.</span>
                      <span className="text-gray-700">{question}</span>
                    </li>
                  ))}
                </ol>
              </div>
              
              <div className="mt-4 flex space-x-2">
                <button className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700">
                  查看原文
                </button>
                <button className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">
                  提交答案
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 bg-yellow-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold text-gray-900 mb-3 flex items-center">
            <span className="material-icons-outlined text-yellow-600 mr-2">tips_and_updates</span>
            听力技巧
          </h2>
          <ul className="space-y-2 text-gray-700">
            <li>• 第一遍听大意，不要纠结于单个词汇</li>
            <li>• 注意关键词和语调变化</li>
            <li>• 可以多听几遍，逐步理解细节</li>
            <li>• 记录不懂的词汇，课后查询</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
