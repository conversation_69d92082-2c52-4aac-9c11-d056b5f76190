export default function StudyNotes() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-6 flex items-center">
          <span className="material-icons-outlined text-blue-600 mr-3">school</span>
          日语学习回顾
        </h1>
        
        <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-8">
          <div className="flex items-center">
            <span className="material-icons-outlined text-blue-600 mr-2">event_note</span>
            <p className="text-gray-700">
              这是一份根据老师在 <strong>水曜日 7:40</strong> 发送的消息整理的学习笔记。
            </p>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm">
            <thead>
              <tr className="bg-gray-50">
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">类别</th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">原文</th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">说明与注解</th>
              </tr>
            </thead>
            <tbody>
              <tr className="hover:bg-gray-50">
                <td className="px-6 py-4 border-b">
                  <div className="flex items-center">
                    <span className="material-icons-outlined text-blue-500 mr-2">volume_up</span>
                    发音
                  </div>
                </td>
                <td className="px-6 py-4 border-b font-medium text-blue-800">簡単</td>
                <td className="px-6 py-4 border-b text-gray-700">发音重点练习词：kantan (简单)</td>
              </tr>
              <tr className="hover:bg-gray-50">
                <td className="px-6 py-4 border-b">
                  <div className="flex items-center">
                    <span className="material-icons-outlined text-blue-500 mr-2">volume_up</span>
                    发音
                  </div>
                </td>
                <td className="px-6 py-4 border-b font-medium text-blue-800">パートナー</td>
                <td className="px-6 py-4 border-b text-gray-700">发音重点练习词：pātonā (伙伴、搭档)</td>
              </tr>
              <tr className="hover:bg-gray-50">
                <td className="px-6 py-4 border-b">
                  <div className="flex items-center">
                    <span className="material-icons-outlined text-green-500 mr-2">chat_bubble_outline</span>
                    表现
                  </div>
                </td>
                <td className="px-6 py-4 border-b font-medium text-blue-800">仕入れ先</td>
                <td className="px-6 py-4 border-b text-gray-700">词汇学习：しいれさき (供应商)</td>
              </tr>
              <tr className="hover:bg-gray-50">
                <td className="px-6 py-4 border-b">
                  <div className="flex items-center">
                    <span className="material-icons-outlined text-green-500 mr-2">chat_bubble_outline</span>
                    表现
                  </div>
                </td>
                <td className="px-6 py-4 border-b">
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-400 line-through font-medium">政権交代</span>
                    <span className="text-red-500 font-bold">→</span>
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded font-medium">世代交代</span>
                  </div>
                </td>
                <td className="px-6 py-4 border-b text-gray-700">用法辨析：老师提示将"政权更迭"联想到"世代交替"。</td>
              </tr>
              <tr className="hover:bg-gray-50">
                <td className="px-6 py-4 border-b">
                  <div className="flex items-center">
                    <span className="material-icons-outlined text-green-500 mr-2">chat_bubble_outline</span>
                    表现
                  </div>
                </td>
                <td className="px-6 py-4 border-b font-medium text-blue-800">終身雇用</td>
                <td className="px-6 py-4 border-b text-gray-700">词汇学习：しゅうしんこよう (终身雇佣制)</td>
              </tr>
              <tr className="hover:bg-gray-50">
                <td className="px-6 py-4 border-b">
                  <div className="flex items-center">
                    <span className="material-icons-outlined text-green-500 mr-2">chat_bubble_outline</span>
                    表现
                  </div>
                </td>
                <td className="px-6 py-4 border-b">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-blue-800">将来性</span>
                    <span className="text-green-600 font-bold">≒</span>
                    <span className="font-medium text-blue-800">伸びしろ</span>
                  </div>
                </td>
                <td className="px-6 py-4 border-b text-gray-700">近义词辨析："未来潜力" (しょうらいせい) 与 "成长空间" (のびしろ) 意思相近。</td>
              </tr>
              <tr className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <div className="flex items-center">
                    <span className="material-icons-outlined text-green-500 mr-2">chat_bubble_outline</span>
                    表现
                  </div>
                </td>
                <td className="px-6 py-4 font-medium text-blue-800">地雷を踏む</td>
                <td className="px-6 py-4 text-gray-700">惯用语：じらいをふむ，字面意思是"踩到地雷"，引申为"触及敏感或危险的话题"。</td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div className="mt-8 text-center bg-yellow-50 p-4 rounded-lg">
          <div className="flex items-center justify-center">
            <span className="material-icons-outlined text-yellow-600 mr-2">forward_to_inbox</span>
            <p className="text-gray-700 italic">
              老师的结束语："では、また明日の授業楽しみにしております。" (那么，期待明天的课。)
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
