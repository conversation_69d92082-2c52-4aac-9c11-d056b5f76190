import { getUserPages } from '@/lib/auth'
import Layout from '@/components/Layout'
import StudyNotes from '@/components/StudyNotes'
import VocabularyTable from '@/components/VocabularyTable'
import NewLesson from '@/components/NewLesson'
import TodayLesson from '@/components/TodayLesson'
import { notFound } from 'next/navigation'
import fs from 'fs'
import path from 'path'

interface PageProps {
  params: Promise<{ student: string; page: string }>
}

export default async function DynamicPage({ params }: PageProps) {
  const { student, page } = await params
  const pages = getUserPages(student)

  // 检查页面是否存在
  if (!pages[page]) {
    notFound()
  }

  const pageInfo = pages[page]

  // 获取课程数据（如果是课程页面）
  const getLessonData = () => {
    if (pageInfo.type === 'lesson' && pageInfo.lessonId) {
      try {
        const lessonsPath = path.join(process.cwd(), 'data', 'lessons.json')
        if (fs.existsSync(lessonsPath)) {
          const lessonsData = fs.readFileSync(lessonsPath, 'utf8')
          const { lessons } = JSON.parse(lessonsData)
          return lessons.find((lesson: any) => lesson.id === pageInfo.lessonId)
        }
      } catch (error) {
        console.error('获取课程数据失败:', error)
      }
    }
    return null
  }

  const lessonData = getLessonData()

  // 根据页面类型渲染不同的内容
  const renderContent = () => {
    switch (page) {
      case 'cet6':
        return <StudyNotes />
      case 'jp':
        return <VocabularyTable />
      case 'new-lesson':
        return <NewLesson />
      case 'vocabulary':
        return (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-6 flex items-center">
                <span className="material-icons-outlined text-blue-600 mr-3">quiz</span>
                词汇练习
              </h1>
              <div className="text-center py-12">
                <span className="material-icons-outlined text-gray-400 text-6xl mb-4">
                  construction
                </span>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  功能开发中
                </h3>
                <p className="text-gray-600">
                  词汇练习功能正在开发中，敬请期待！
                </p>
              </div>
            </div>
          </div>
        )
      case 'grammar':
        return (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-6 flex items-center">
                <span className="material-icons-outlined text-blue-600 mr-3">auto_stories</span>
                日语语法学习
              </h1>
              <div className="space-y-6">
                <div className="bg-blue-50 p-6 rounded-lg">
                  <h2 className="text-xl font-semibold text-gray-900 mb-3">基础语法</h2>
                  <p className="text-gray-700">
                    这里将包含日语基础语法规则和例句。
                  </p>
                </div>
                <div className="bg-green-50 p-6 rounded-lg">
                  <h2 className="text-xl font-semibold text-gray-900 mb-3">进阶语法</h2>
                  <p className="text-gray-700">
                    这里将包含日语进阶语法内容。
                  </p>
                </div>
              </div>
            </div>
          </div>
        )
      case 'reading':
        return (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-6 flex items-center">
                <span className="material-icons-outlined text-blue-600 mr-3">menu_book</span>
                阅读理解
              </h1>
              <div className="space-y-6">
                <div className="bg-yellow-50 p-6 rounded-lg">
                  <h2 className="text-xl font-semibold text-gray-900 mb-3">阅读材料</h2>
                  <p className="text-gray-700">
                    这里将包含各种日语阅读材料和练习。
                  </p>
                </div>
              </div>
            </div>
          </div>
        )
      default:
        // 检查是否为课程页面
        if (pageInfo.type === 'lesson') {
          return <TodayLesson lesson={lessonData} />
        }

        return (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-6">
                {pageInfo.title}
              </h1>
              <p className="text-gray-600">
                {pageInfo.description}
              </p>
            </div>
          </div>
        )
    }
  }

  return (
    <Layout studentId={student} currentPage={page} pages={pages}>
      {renderContent()}
    </Layout>
  )
}
