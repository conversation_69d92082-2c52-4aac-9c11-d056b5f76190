'use client'

import { useState } from 'react'

interface PronunciationItem {
  word: string
  reading: string
  meaning: string
}

interface ExpressionItem {
  expression: string
  reading: string
  meaning: string
  usage: string
  example: string
}

export default function TeacherDashboard() {
  const [lessonTitle, setLessonTitle] = useState('')
  const [selectedStudents, setSelectedStudents] = useState<string[]>([])
  const [pronunciationItems, setPronunciationItems] = useState<PronunciationItem[]>([
    { word: '', reading: '', meaning: '' }
  ])
  const [expressionItems, setExpressionItems] = useState<ExpressionItem[]>([
    { expression: '', reading: '', meaning: '', usage: '', example: '' }
  ])
  const [homework, setHomework] = useState('')
  const [notes, setNotes] = useState('')

  const students = [
    { id: 'student1', name: '学生一' },
    { id: 'student2', name: '学生二' }
  ]

  const addPronunciationItem = () => {
    setPronunciationItems([...pronunciationItems, { word: '', reading: '', meaning: '' }])
  }

  const addExpressionItem = () => {
    setExpressionItems([...expressionItems, { expression: '', reading: '', meaning: '', usage: '', example: '' }])
  }

  const updatePronunciationItem = (index: number, field: keyof PronunciationItem, value: string) => {
    const updated = [...pronunciationItems]
    updated[index][field] = value
    setPronunciationItems(updated)
  }

  const updateExpressionItem = (index: number, field: keyof ExpressionItem, value: string) => {
    const updated = [...expressionItems]
    updated[index][field] = value
    setExpressionItems(updated)
  }

  const removePronunciationItem = (index: number) => {
    setPronunciationItems(pronunciationItems.filter((_, i) => i !== index))
  }

  const removeExpressionItem = (index: number) => {
    setExpressionItems(expressionItems.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const lessonData = {
      title: lessonTitle,
      students: selectedStudents,
      content: {
        pronunciation: {
          title: "今日の発音",
          items: pronunciationItems.filter(item => item.word.trim())
        },
        expressions: {
          title: "今日の表現", 
          items: expressionItems.filter(item => item.expression.trim())
        }
      },
      homework,
      notes
    }

    try {
      const response = await fetch('/api/lessons', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(lessonData)
      })

      if (response.ok) {
        alert('课程发布成功！')
        // 重置表单
        setLessonTitle('')
        setSelectedStudents([])
        setPronunciationItems([{ word: '', reading: '', meaning: '' }])
        setExpressionItems([{ expression: '', reading: '', meaning: '', usage: '', example: '' }])
        setHomework('')
        setNotes('')
      } else {
        alert('发布失败，请重试')
      }
    } catch (error) {
      console.error('发布错误:', error)
      alert('发布失败，请重试')
    }
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 flex items-center">
          <span className="material-icons-outlined text-blue-600 mr-3">school</span>
          教师控制台 - 发布新课程
        </h1>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* 基本信息 */}
          <div className="bg-gray-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <span className="material-icons-outlined text-blue-600 mr-2">info</span>
              课程基本信息
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  课程标题
                </label>
                <input
                  type="text"
                  value={lessonTitle}
                  onChange={(e) => setLessonTitle(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="例：2024年1月15日 - 日常问候"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  发布给学生
                </label>
                <div className="space-y-2">
                  {students.map(student => (
                    <label key={student.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedStudents.includes(student.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedStudents([...selectedStudents, student.id])
                          } else {
                            setSelectedStudents(selectedStudents.filter(id => id !== student.id))
                          }
                        }}
                        className="mr-2"
                      />
                      {student.name}
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 发音练习 */}
          <div className="bg-red-50 p-6 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold flex items-center">
                <span className="material-icons-outlined text-red-600 mr-2">record_voice_over</span>
                今日の発音
              </h2>
              <button
                type="button"
                onClick={addPronunciationItem}
                className="bg-red-600 text-white px-3 py-1 rounded-md hover:bg-red-700 flex items-center"
              >
                <span className="material-icons-outlined mr-1 text-sm">add</span>
                添加
              </button>
            </div>
            <div className="space-y-4">
              {pronunciationItems.map((item, index) => (
                <div key={index} className="bg-white p-4 rounded border grid grid-cols-1 md:grid-cols-4 gap-3">
                  <input
                    type="text"
                    placeholder="日语单词"
                    value={item.word}
                    onChange={(e) => updatePronunciationItem(index, 'word', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                  <input
                    type="text"
                    placeholder="读音 (romaji)"
                    value={item.reading}
                    onChange={(e) => updatePronunciationItem(index, 'reading', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                  <input
                    type="text"
                    placeholder="中文意思"
                    value={item.meaning}
                    onChange={(e) => updatePronunciationItem(index, 'meaning', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                  <button
                    type="button"
                    onClick={() => removePronunciationItem(index)}
                    className="bg-red-500 text-white px-3 py-2 rounded-md hover:bg-red-600"
                  >
                    删除
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* 表达练习 */}
          <div className="bg-green-50 p-6 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold flex items-center">
                <span className="material-icons-outlined text-green-600 mr-2">chat_bubble</span>
                今日の表現
              </h2>
              <button
                type="button"
                onClick={addExpressionItem}
                className="bg-green-600 text-white px-3 py-1 rounded-md hover:bg-green-700 flex items-center"
              >
                <span className="material-icons-outlined mr-1 text-sm">add</span>
                添加
              </button>
            </div>
            <div className="space-y-4">
              {expressionItems.map((item, index) => (
                <div key={index} className="bg-white p-4 rounded border space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <input
                      type="text"
                      placeholder="日语表达"
                      value={item.expression}
                      onChange={(e) => updateExpressionItem(index, 'expression', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                    <input
                      type="text"
                      placeholder="读音 (romaji)"
                      value={item.reading}
                      onChange={(e) => updateExpressionItem(index, 'reading', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                    <input
                      type="text"
                      placeholder="中文意思"
                      value={item.meaning}
                      onChange={(e) => updateExpressionItem(index, 'meaning', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <input
                      type="text"
                      placeholder="使用场合"
                      value={item.usage}
                      onChange={(e) => updateExpressionItem(index, 'usage', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                    <input
                      type="text"
                      placeholder="例句"
                      value={item.example}
                      onChange={(e) => updateExpressionItem(index, 'example', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={() => removeExpressionItem(index)}
                    className="bg-red-500 text-white px-3 py-1 rounded-md hover:bg-red-600"
                  >
                    删除
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* 作业和备注 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-yellow-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <span className="material-icons-outlined text-yellow-600 mr-2">assignment</span>
                宿題
              </h2>
              <textarea
                value={homework}
                onChange={(e) => setHomework(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500"
                rows={4}
                placeholder="布置作业内容..."
              />
            </div>
            <div className="bg-blue-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <span className="material-icons-outlined text-blue-600 mr-2">note</span>
                注意事項
              </h2>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={4}
                placeholder="学习要点和注意事项..."
              />
            </div>
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-center">
            <button
              type="submit"
              className="bg-blue-600 text-white px-8 py-3 rounded-md hover:bg-blue-700 flex items-center text-lg font-medium"
            >
              <span className="material-icons-outlined mr-2">publish</span>
              发布课程
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
