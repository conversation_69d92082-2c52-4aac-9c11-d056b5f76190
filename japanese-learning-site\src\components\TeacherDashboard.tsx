'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Logo from './Logo'

interface PronunciationItem {
  word: string
  reading: string
  meaning: string
}

interface ExpressionItem {
  expression: string
  reading: string
  meaning: string
  usage: string
  example: string
  correction?: {
    wrong: string
    correct: string
    note: string
  }
}

export default function TeacherDashboard() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('publish') // publish, history, students
  const [lessonTitle, setLessonTitle] = useState('')
  const [selectedStudents, setSelectedStudents] = useState<string[]>([])
  const [pronunciationItems, setPronunciationItems] = useState<PronunciationItem[]>([
    { word: '', reading: '', meaning: '' }
  ])
  const [expressionItems, setExpressionItems] = useState<ExpressionItem[]>([
    { expression: '', reading: '', meaning: '', usage: '', example: '' }
  ])
  const [homework, setHomework] = useState('')
  const [notes, setNotes] = useState('')

  // 数据状态
  const [students, setStudents] = useState<any[]>([])
  const [lessons, setLessons] = useState<any[]>([])
  const [selectedStudent, setSelectedStudent] = useState('')

  // 新学生表单
  const [newStudentForm, setNewStudentForm] = useState({
    username: '',
    name: '',
    password: ''
  })
  const [showAddStudentForm, setShowAddStudentForm] = useState(false)

  // 学生详情管理
  const [selectedStudentDetail, setSelectedStudentDetail] = useState<any>(null)
  const [showStudentDetail, setShowStudentDetail] = useState(false)
  const [editStudentForm, setEditStudentForm] = useState({
    name: '',
    username: '',
    password: '',
    evaluation: ''
  })

  // 课程编辑
  const [selectedLessonEdit, setSelectedLessonEdit] = useState<any>(null)
  const [showLessonEdit, setShowLessonEdit] = useState(false)

  // 批量导入
  const [showBatchImport, setShowBatchImport] = useState(false)
  const [batchImportText, setBatchImportText] = useState('')
  const [importType, setImportType] = useState<'pronunciation' | 'expression'>('pronunciation')

  // 注销登录
  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' })
      router.push('/login')
    } catch (error) {
      console.error('登出失败:', error)
    }
  }

  // 获取数据
  useEffect(() => {
    fetchStudents()
    fetchLessons()
  }, [])

  const fetchStudents = async () => {
    try {
      const response = await fetch('/api/students')
      const data = await response.json()
      if (data.students) {
        setStudents(data.students)
      }
    } catch (error) {
      console.error('获取学生列表失败:', error)
    }
  }

  const fetchLessons = async () => {
    try {
      const response = await fetch('/api/lessons')
      const data = await response.json()
      if (data.lessons) {
        setLessons(data.lessons)
      }
    } catch (error) {
      console.error('获取课程列表失败:', error)
    }
  }

  // 添加新学生
  const handleAddStudent = async (e: React.FormEvent) => {
    e.preventDefault()

    const trimmedForm = {
      username: newStudentForm.username.trim(),
      name: newStudentForm.name.trim(),
      password: newStudentForm.password.trim()
    }

    if (!trimmedForm.username || !trimmedForm.name || !trimmedForm.password) {
      alert('所有字段都不能为空')
      return
    }

    try {
      const response = await fetch('/api/students', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(trimmedForm)
      })

      const data = await response.json()

      if (data.success) {
        alert('学生添加成功！')
        setNewStudentForm({ username: '', name: '', password: '' })
        setShowAddStudentForm(false)
        fetchStudents() // 刷新学生列表
      } else {
        alert(data.error || '添加学生失败')
      }
    } catch (error) {
      console.error('添加学生失败:', error)
      alert('添加学生失败')
    }
  }

  // 查看学生详情
  const handleViewStudentDetail = async (studentId: string) => {
    try {
      const response = await fetch(`/api/students/${studentId}`)
      const data = await response.json()

      if (data.student) {
        setSelectedStudentDetail(data)
        setEditStudentForm({
          name: data.student.name,
          username: data.student.username,
          password: '',
          evaluation: data.student.evaluation || ''
        })
        setShowStudentDetail(true)
      }
    } catch (error) {
      console.error('获取学生详情失败:', error)
    }
  }

  // 更新学生信息
  const handleUpdateStudent = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedStudentDetail) return

    try {
      const response = await fetch(`/api/students/${selectedStudentDetail.student.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editStudentForm)
      })

      const data = await response.json()

      if (data.success) {
        alert('学生信息更新成功！')
        setShowStudentDetail(false)
        fetchStudents() // 刷新学生列表
      } else {
        alert(data.error || '更新失败')
      }
    } catch (error) {
      console.error('更新学生信息失败:', error)
      alert('更新失败')
    }
  }

  // 删除学生
  const handleDeleteStudent = async (studentId: string, studentName: string) => {
    if (!confirm(`确定要删除学生 "${studentName}" 吗？此操作不可恢复！`)) {
      return
    }

    try {
      const response = await fetch(`/api/students/${studentId}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        alert('学生删除成功！')
        setShowStudentDetail(false)
        fetchStudents() // 刷新学生列表
      } else {
        alert(data.error || '删除失败')
      }
    } catch (error) {
      console.error('删除学生失败:', error)
      alert('删除失败')
    }
  }

  // 删除课程
  const handleDeleteLesson = async (lessonId: string, lessonTitle: string) => {
    if (!confirm(`确定要删除课程 "${lessonTitle}" 吗？此操作不可恢复！`)) {
      return
    }

    try {
      const response = await fetch(`/api/lessons/${lessonId}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        alert('课程删除成功！')
        fetchLessons() // 刷新课程列表
      } else {
        alert(data.error || '删除失败')
      }
    } catch (error) {
      console.error('删除课程失败:', error)
      alert('删除失败')
    }
  }

  const addPronunciationItem = () => {
    setPronunciationItems([...pronunciationItems, { word: '', reading: '', meaning: '' }])
  }

  const addExpressionItem = () => {
    setExpressionItems([...expressionItems, { expression: '', reading: '', meaning: '', usage: '', example: '' }])
  }

  const updatePronunciationItem = (index: number, field: keyof PronunciationItem, value: string) => {
    const updated = [...pronunciationItems]
    updated[index][field] = value
    setPronunciationItems(updated)
  }

  const updateExpressionItem = (index: number, field: keyof ExpressionItem, value: string) => {
    const updated = [...expressionItems]
    updated[index][field] = value
    setExpressionItems(updated)
  }

  const removePronunciationItem = (index: number) => {
    setPronunciationItems(pronunciationItems.filter((_, i) => i !== index))
  }

  const removeExpressionItem = (index: number) => {
    setExpressionItems(expressionItems.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const lessonData = {
      title: lessonTitle,
      students: selectedStudents,
      content: {
        pronunciation: {
          title: "今日の発音",
          items: pronunciationItems.filter(item => item.word.trim())
        },
        expressions: {
          title: "今日の表現", 
          items: expressionItems.filter(item => item.expression.trim())
        }
      },
      homework,
      notes
    }

    try {
      const response = await fetch('/api/lessons', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(lessonData)
      })

      if (response.ok) {
        alert('课程发布成功！')
        // 重置表单
        setLessonTitle('')
        setSelectedStudents([])
        setPronunciationItems([{ word: '', reading: '', meaning: '' }])
        setExpressionItems([{ expression: '', reading: '', meaning: '', usage: '', example: '' }])
        setHomework('')
        setNotes('')
        fetchLessons() // 刷新课程列表
      } else {
        alert('发布失败，请重试')
      }
    } catch (error) {
      console.error('发布错误:', error)
      alert('发布失败，请重试')
    }
  }

  // 过滤课程（按学生）
  const filteredLessons = selectedStudent
    ? lessons.filter(lesson => lesson.students.includes(selectedStudent))
    : lessons

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg">
        {/* 标题栏 */}
        <div className="border-b border-gray-200 px-8 py-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Logo userRole="teacher" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">教师控制台</h1>
                <p className="text-sm text-gray-600">管理学生和发布课程</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center px-4 py-2 text-sm text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors"
            >
              <span className="material-icons-outlined mr-2 text-sm">logout</span>
              注销登录
            </button>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-8">
            <button
              onClick={() => setActiveTab('publish')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'publish'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="material-icons-outlined mr-2 align-middle">publish</span>
              发布课程
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'history'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="material-icons-outlined mr-2 align-middle">history</span>
              历史记录
            </button>
            <button
              onClick={() => setActiveTab('students')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'students'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="material-icons-outlined mr-2 align-middle">people</span>
              学生管理
            </button>
          </nav>
        </div>

        {/* 内容区域 */}
        <div className="p-8">{renderTabContent()}</div>
      </div>

      {/* 学生详情管理模态框 */}
      {showStudentDetail && selectedStudentDetail && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">学生详情管理</h3>
            </div>
            <div className="p-6">
              <form onSubmit={handleUpdateStudent} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      姓名
                    </label>
                    <input
                      type="text"
                      value={editStudentForm.name}
                      onChange={(e) => setEditStudentForm({...editStudentForm, name: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      用户名
                    </label>
                    <input
                      type="text"
                      value={editStudentForm.username}
                      onChange={(e) => setEditStudentForm({...editStudentForm, username: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    新密码（留空则不修改）
                  </label>
                  <input
                    type="password"
                    value={editStudentForm.password}
                    onChange={(e) => setEditStudentForm({...editStudentForm, password: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="输入新密码或留空保持不变"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    学习评价
                  </label>
                  <textarea
                    value={editStudentForm.evaluation}
                    onChange={(e) => setEditStudentForm({...editStudentForm, evaluation: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={4}
                    placeholder="对学生的学习情况进行评价..."
                  />
                </div>

                {/* 学生统计信息 */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">学习统计</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">已分配课程:</span>
                      <span className="ml-2 font-medium">{selectedStudentDetail.lessons.length} 个</span>
                    </div>
                    <div>
                      <span className="text-gray-600">注册时间:</span>
                      <span className="ml-2 font-medium">
                        {new Date(selectedStudentDetail.student.createdAt || Date.now()).toLocaleDateString('zh-CN')}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between pt-4">
                  <button
                    type="button"
                    onClick={() => handleDeleteStudent(selectedStudentDetail.student.id, selectedStudentDetail.student.name)}
                    className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
                  >
                    删除学生
                  </button>
                  <div className="flex space-x-4">
                    <button
                      type="button"
                      onClick={() => setShowStudentDetail(false)}
                      className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
                    >
                      取消
                    </button>
                    <button
                      type="submit"
                      className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                    >
                      保存修改
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )

  function renderTabContent() {
    switch (activeTab) {
      case 'publish':
        return renderPublishTab()
      case 'history':
        return renderHistoryTab()
      case 'students':
        return renderStudentsTab()
      default:
        return renderPublishTab()
    }
  }

  function renderPublishTab() {
    return (

        <div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">发布新课程</h2>
          <form onSubmit={handleSubmit} className="space-y-8">
          {/* 基本信息 */}
          <div className="bg-gray-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <span className="material-icons-outlined text-blue-600 mr-2">info</span>
              课程基本信息
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  课程标题
                </label>
                <input
                  type="text"
                  value={lessonTitle}
                  onChange={(e) => setLessonTitle(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="例：2024年1月15日 - 日常问候"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  发布给学生
                </label>
                <div className="space-y-2">
                  {students.map(student => (
                    <label key={student.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedStudents.includes(student.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedStudents([...selectedStudents, student.id])
                          } else {
                            setSelectedStudents(selectedStudents.filter(id => id !== student.id))
                          }
                        }}
                        className="mr-2"
                      />
                      {student.name}
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 发音练习 */}
          <div className="bg-red-50 p-6 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold flex items-center">
                <span className="material-icons-outlined text-red-600 mr-2">record_voice_over</span>
                今日の発音
              </h2>
              <button
                type="button"
                onClick={addPronunciationItem}
                className="bg-red-600 text-white px-3 py-1 rounded-md hover:bg-red-700 flex items-center"
              >
                <span className="material-icons-outlined mr-1 text-sm">add</span>
                添加
              </button>
            </div>
            <div className="space-y-4">
              {pronunciationItems.map((item, index) => (
                <div key={index} className="bg-white p-4 rounded border grid grid-cols-1 md:grid-cols-4 gap-3">
                  <input
                    type="text"
                    placeholder="日语单词"
                    value={item.word}
                    onChange={(e) => updatePronunciationItem(index, 'word', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                  <input
                    type="text"
                    placeholder="读音 (romaji)"
                    value={item.reading}
                    onChange={(e) => updatePronunciationItem(index, 'reading', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                  <input
                    type="text"
                    placeholder="中文意思"
                    value={item.meaning}
                    onChange={(e) => updatePronunciationItem(index, 'meaning', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                  <button
                    type="button"
                    onClick={() => removePronunciationItem(index)}
                    className="bg-red-500 text-white px-3 py-2 rounded-md hover:bg-red-600"
                  >
                    删除
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* 表达练习 */}
          <div className="bg-green-50 p-6 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold flex items-center">
                <span className="material-icons-outlined text-green-600 mr-2">chat_bubble</span>
                今日の表現
              </h2>
              <button
                type="button"
                onClick={addExpressionItem}
                className="bg-green-600 text-white px-3 py-1 rounded-md hover:bg-green-700 flex items-center"
              >
                <span className="material-icons-outlined mr-1 text-sm">add</span>
                添加
              </button>
            </div>
            <div className="space-y-4">
              {expressionItems.map((item, index) => (
                <div key={index} className="bg-white p-4 rounded border space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <input
                      type="text"
                      placeholder="日语表达"
                      value={item.expression}
                      onChange={(e) => updateExpressionItem(index, 'expression', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                    <input
                      type="text"
                      placeholder="读音 (romaji)"
                      value={item.reading}
                      onChange={(e) => updateExpressionItem(index, 'reading', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                    <input
                      type="text"
                      placeholder="中文意思"
                      value={item.meaning}
                      onChange={(e) => updateExpressionItem(index, 'meaning', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <input
                      type="text"
                      placeholder="使用场合"
                      value={item.usage}
                      onChange={(e) => updateExpressionItem(index, 'usage', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                    <input
                      type="text"
                      placeholder="例句"
                      value={item.example}
                      onChange={(e) => updateExpressionItem(index, 'example', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={() => removeExpressionItem(index)}
                    className="bg-red-500 text-white px-3 py-1 rounded-md hover:bg-red-600"
                  >
                    删除
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* 作业和备注 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-yellow-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <span className="material-icons-outlined text-yellow-600 mr-2">assignment</span>
                宿題
              </h2>
              <textarea
                value={homework}
                onChange={(e) => setHomework(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500"
                rows={4}
                placeholder="布置作业内容..."
              />
            </div>
            <div className="bg-blue-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <span className="material-icons-outlined text-blue-600 mr-2">note</span>
                注意事項
              </h2>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={4}
                placeholder="学习要点和注意事项..."
              />
            </div>
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-center">
            <button
              type="submit"
              className="bg-blue-600 text-white px-8 py-3 rounded-md hover:bg-blue-700 flex items-center text-lg font-medium"
            >
              <span className="material-icons-outlined mr-2">publish</span>
              发布课程
            </button>
          </div>
        </form>
        </div>
    )
  }

  function renderHistoryTab() {
    return (
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold text-gray-900">历史发布记录</h2>
          <div className="flex items-center space-x-4">
            <select
              value={selectedStudent}
              onChange={(e) => setSelectedStudent(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">所有学生</option>
              {students.map(student => (
                <option key={student.id} value={student.id}>
                  {student.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {filteredLessons.length === 0 ? (
          <div className="text-center py-12">
            <span className="material-icons-outlined text-gray-400 text-6xl mb-4">
              history_edu
            </span>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              暂无课程记录
            </h3>
            <p className="text-gray-600">
              {selectedStudent ? '该学生还没有课程记录' : '还没有发布任何课程'}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredLessons.map((lesson) => (
              <div key={lesson.id} className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{lesson.title}</h3>
                    <p className="text-sm text-gray-600">
                      发布时间: {new Date(lesson.createdAt).toLocaleString('zh-CN')}
                    </p>
                  </div>
                  <div className="text-sm text-gray-500">
                    课程ID: {lesson.id}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                      <span className="material-icons-outlined text-red-600 mr-2">record_voice_over</span>
                      今日の発音 ({lesson.content.pronunciation.items.length}项)
                    </h4>
                    <div className="space-y-1">
                      {lesson.content.pronunciation.items.slice(0, 3).map((item: any, index: number) => (
                        <p key={index} className="text-sm text-gray-700">
                          • {item.word} - {item.meaning}
                        </p>
                      ))}
                      {lesson.content.pronunciation.items.length > 3 && (
                        <p className="text-sm text-gray-500">
                          ...还有 {lesson.content.pronunciation.items.length - 3} 项
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                      <span className="material-icons-outlined text-green-600 mr-2">chat_bubble</span>
                      今日の表現 ({lesson.content.expressions.items.length}项)
                    </h4>
                    <div className="space-y-1">
                      {lesson.content.expressions.items.slice(0, 3).map((item: any, index: number) => (
                        <p key={index} className="text-sm text-gray-700">
                          • {item.expression} - {item.meaning}
                        </p>
                      ))}
                      {lesson.content.expressions.items.length > 3 && (
                        <p className="text-sm text-gray-500">
                          ...还有 {lesson.content.expressions.items.length - 3} 项
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="border-t border-gray-200 pt-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-600">
                        发布给: {lesson.students.map((studentId: string) => {
                          const student = students.find(s => s.id === studentId)
                          return student?.name || studentId
                        }).join(', ')}
                      </span>
                      <div className="flex space-x-2">
                        {lesson.homework && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <span className="material-icons-outlined mr-1 text-xs">assignment</span>
                            有作业
                          </span>
                        )}
                        {lesson.notes && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <span className="material-icons-outlined mr-1 text-xs">note</span>
                            有备注
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedLessonEdit(lesson)
                          setShowLessonEdit(true)
                        }}
                        className="bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 flex items-center text-sm"
                      >
                        <span className="material-icons-outlined mr-1 text-sm">edit</span>
                        编辑
                      </button>
                      <button
                        onClick={() => handleDeleteLesson(lesson.id, lesson.title)}
                        className="bg-red-600 text-white px-3 py-1 rounded-md hover:bg-red-700 flex items-center text-sm"
                      >
                        <span className="material-icons-outlined mr-1 text-sm">delete</span>
                        删除
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    )
  }

  function renderStudentsTab() {
    return (
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold text-gray-900">学生管理</h2>
          <button
            onClick={() => setShowAddStudentForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
          >
            <span className="material-icons-outlined mr-2">person_add</span>
            添加学生
          </button>
        </div>

        {/* 添加学生表单 */}
        {showAddStudentForm && (
          <div className="mb-8 bg-blue-50 p-6 rounded-lg border border-blue-200">
            <h3 className="text-lg font-semibold mb-4">添加新学生</h3>
            <form onSubmit={handleAddStudent} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    用户名
                  </label>
                  <input
                    type="text"
                    value={newStudentForm.username}
                    onChange={(e) => setNewStudentForm({...newStudentForm, username: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="学生登录用户名"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    姓名
                  </label>
                  <input
                    type="text"
                    value={newStudentForm.name}
                    onChange={(e) => setNewStudentForm({...newStudentForm, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="学生真实姓名"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    初始密码
                  </label>
                  <input
                    type="password"
                    value={newStudentForm.password}
                    onChange={(e) => setNewStudentForm({...newStudentForm, password: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="设置初始密码"
                    required
                  />
                </div>
              </div>
              <div className="flex space-x-4">
                <button
                  type="submit"
                  className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
                >
                  添加学生
                </button>
                <button
                  type="button"
                  onClick={() => setShowAddStudentForm(false)}
                  className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
                >
                  取消
                </button>
              </div>
            </form>
          </div>
        )}

        {/* 学生列表 */}
        <div className="grid gap-4">
          {students.length === 0 ? (
            <div className="text-center py-12">
              <span className="material-icons-outlined text-gray-400 text-6xl mb-4">
                people_outline
              </span>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                暂无学生
              </h3>
              <p className="text-gray-600">
                点击上方按钮添加第一个学生
              </p>
            </div>
          ) : (
            students.map((student) => {
              const studentLessons = lessons.filter(lesson => lesson.students.includes(student.id))
              return (
                <div key={student.id} className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{student.name}</h3>
                      <p className="text-sm text-gray-600">用户名: {student.username}</p>
                      <p className="text-sm text-gray-600">学生ID: {student.id}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-600 mb-2">
                        已发布课程: {studentLessons.length} 个
                      </div>
                      <div className="text-xs text-gray-500 mb-3">
                        最近课程: {studentLessons.length > 0
                          ? new Date(studentLessons[0].createdAt).toLocaleDateString('zh-CN')
                          : '无'
                        }
                      </div>
                      <button
                        onClick={() => handleViewStudentDetail(student.id)}
                        className="bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 flex items-center text-sm"
                      >
                        <span className="material-icons-outlined mr-1 text-sm">visibility</span>
                        详情管理
                      </button>
                    </div>
                  </div>

                  {studentLessons.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">最近课程:</h4>
                      <div className="space-y-1">
                        {studentLessons.slice(0, 3).map((lesson) => (
                          <div key={lesson.id} className="text-sm text-gray-700 flex justify-between">
                            <span>{lesson.title}</span>
                            <span className="text-gray-500">
                              {new Date(lesson.createdAt).toLocaleDateString('zh-CN')}
                            </span>
                          </div>
                        ))}
                        {studentLessons.length > 3 && (
                          <p className="text-xs text-gray-500">
                            ...还有 {studentLessons.length - 3} 个课程
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )
            })
          )}
        </div>
      </div>
    )
  }
}
