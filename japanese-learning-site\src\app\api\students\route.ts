import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/lib/auth'
import bcrypt from 'bcryptjs'
import fs from 'fs'
import path from 'path'

// 获取学生列表
export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value
    
    if (!token) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }

    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: '无效token' }, { status: 401 })
    }

    // 验证是否为教师
    const usersPath = path.join(process.cwd(), 'data', 'users.json')
    const usersData = fs.readFileSync(usersPath, 'utf8')
    const users = JSON.parse(usersData)
    const user = users[decoded.userId]

    if (!user || user.role !== 'teacher') {
      return NextResponse.json({ error: '只有教师可以查看学生列表' }, { status: 403 })
    }

    // 获取该教师的所有学生
    const students = Object.values(users).filter((u: any) => 
      u.role === 'student' && u.teacher === decoded.userId
    )

    return NextResponse.json({ students })
  } catch (error) {
    console.error('获取学生列表错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// 添加新学生
export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value
    
    if (!token) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }

    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: '无效token' }, { status: 401 })
    }

    // 验证是否为教师
    const usersPath = path.join(process.cwd(), 'data', 'users.json')
    const usersData = fs.readFileSync(usersPath, 'utf8')
    const users = JSON.parse(usersData)
    const user = users[decoded.userId]

    if (!user || user.role !== 'teacher') {
      return NextResponse.json({ error: '只有教师可以添加学生' }, { status: 403 })
    }

    const { username, name, password } = await request.json()

    // 验证输入
    if (!username?.trim() || !name?.trim() || !password?.trim()) {
      return NextResponse.json({ error: '用户名、姓名和密码不能为空' }, { status: 400 })
    }

    const trimmedUsername = username.trim()
    const trimmedName = name.trim()
    const trimmedPassword = password.trim()

    // 检查用户名是否已存在
    if (users[trimmedUsername] || Object.values(users).some((u: any) => u.username === trimmedUsername)) {
      return NextResponse.json({ error: '用户名已存在' }, { status: 400 })
    }

    // 生成学生ID
    const studentId = `student_${Date.now()}`
    
    // 加密密码
    const hashedPassword = await bcrypt.hash(trimmedPassword, 10)

    // 创建新学生
    const newStudent = {
      id: studentId,
      username: trimmedUsername,
      password: hashedPassword,
      name: trimmedName,
      role: 'student',
      teacher: decoded.userId,
      pages: []
    }

    // 添加到用户数据
    users[studentId] = newStudent

    // 保存用户数据
    fs.writeFileSync(usersPath, JSON.stringify(users, null, 2))

    // 创建学生内容目录
    const studentContentDir = path.join(process.cwd(), 'data', 'content', studentId)
    if (!fs.existsSync(studentContentDir)) {
      fs.mkdirSync(studentContentDir, { recursive: true })
    }

    // 创建学生页面配置文件
    const studentPagesPath = path.join(studentContentDir, 'pages.json')
    const defaultPages = {}
    fs.writeFileSync(studentPagesPath, JSON.stringify(defaultPages, null, 2))

    return NextResponse.json({ 
      success: true, 
      student: {
        id: newStudent.id,
        username: newStudent.username,
        name: newStudent.name,
        role: newStudent.role
      },
      message: '学生添加成功！'
    })
  } catch (error) {
    console.error('添加学生错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
