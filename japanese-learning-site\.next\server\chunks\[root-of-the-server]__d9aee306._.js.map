{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken'\nimport bcrypt from 'bcryptjs'\nimport fs from 'fs'\nimport path from 'path'\n\nexport interface User {\n  id: string\n  username: string\n  password: string\n  name: string\n  role?: string\n  teacher?: string\n  students?: string[]\n  pages?: string[]\n}\n\nexport interface UserData {\n  [key: string]: User\n}\n\n// 获取用户数据\nexport function getUserData(): UserData {\n  const usersPath = path.join(process.cwd(), 'data', 'users.json')\n  const usersData = fs.readFileSync(usersPath, 'utf8')\n  return JSON.parse(usersData)\n}\n\n// 验证用户凭据\nexport async function validateUser(username: string, password: string): Promise<User | null> {\n  const users = getUserData()\n  const user = Object.values(users).find(u => u.username === username)\n\n  if (!user) {\n    return null\n  }\n\n  const isValid = await bcrypt.compare(password, user.password)\n  return isValid ? user : null\n}\n\n// 生成JWT token\nexport function generateToken(userId: string): string {\n  return jwt.sign({ userId }, process.env.JWT_SECRET!, { expiresIn: '7d' })\n}\n\n// 验证JWT token\nexport function verifyToken(token: string): { userId: string } | null {\n  try {\n    return jwt.verify(token, process.env.JWT_SECRET!) as { userId: string }\n  } catch {\n    return null\n  }\n}\n\n// 获取用户页面配置\nexport function getUserPages(userId: string) {\n  try {\n    const pagesPath = path.join(process.cwd(), 'data', 'content', userId, 'pages.json')\n    const pagesData = fs.readFileSync(pagesPath, 'utf8')\n    return JSON.parse(pagesData)\n  } catch {\n    return {}\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;AAkBO,SAAS;IACd,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;IACnD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;IAC7C,OAAO,KAAK,KAAK,CAAC;AACpB;AAGO,eAAe,aAAa,QAAgB,EAAE,QAAgB;IACnE,MAAM,QAAQ;IACd,MAAM,OAAO,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IAE3D,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,UAAU,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ;IAC5D,OAAO,UAAU,OAAO;AAC1B;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC;QAAE;IAAO,GAAG,QAAQ,GAAG,CAAC,UAAU,EAAG;QAAE,WAAW;IAAK;AACzE;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU;IACjD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,aAAa,MAAc;IACzC,IAAI;QACF,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,WAAW,QAAQ;QACtE,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;QAC7C,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO,CAAC;IACV;AACF", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/app/api/students/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { verifyToken } from '@/lib/auth'\nimport bcrypt from 'bcryptjs'\nimport fs from 'fs'\nimport path from 'path'\n\n// 获取学生列表\nexport async function GET(request: NextRequest) {\n  try {\n    const token = request.cookies.get('auth-token')?.value\n    \n    if (!token) {\n      return NextResponse.json({ error: '未授权' }, { status: 401 })\n    }\n\n    const decoded = verifyToken(token)\n    if (!decoded) {\n      return NextResponse.json({ error: '无效token' }, { status: 401 })\n    }\n\n    // 验证是否为教师\n    const usersPath = path.join(process.cwd(), 'data', 'users.json')\n    const usersData = fs.readFileSync(usersPath, 'utf8')\n    const users = JSON.parse(usersData)\n    const user = users[decoded.userId]\n\n    if (!user || user.role !== 'teacher') {\n      return NextResponse.json({ error: '只有教师可以查看学生列表' }, { status: 403 })\n    }\n\n    // 获取该教师的所有学生\n    const students = Object.values(users).filter((u: any) => \n      u.role === 'student' && u.teacher === decoded.userId\n    )\n\n    return NextResponse.json({ students })\n  } catch (error) {\n    console.error('获取学生列表错误:', error)\n    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })\n  }\n}\n\n// 添加新学生\nexport async function POST(request: NextRequest) {\n  try {\n    const token = request.cookies.get('auth-token')?.value\n    \n    if (!token) {\n      return NextResponse.json({ error: '未授权' }, { status: 401 })\n    }\n\n    const decoded = verifyToken(token)\n    if (!decoded) {\n      return NextResponse.json({ error: '无效token' }, { status: 401 })\n    }\n\n    // 验证是否为教师\n    const usersPath = path.join(process.cwd(), 'data', 'users.json')\n    const usersData = fs.readFileSync(usersPath, 'utf8')\n    const users = JSON.parse(usersData)\n    const user = users[decoded.userId]\n\n    if (!user || user.role !== 'teacher') {\n      return NextResponse.json({ error: '只有教师可以添加学生' }, { status: 403 })\n    }\n\n    const { username, name, password } = await request.json()\n\n    // 验证输入\n    if (!username?.trim() || !name?.trim() || !password?.trim()) {\n      return NextResponse.json({ error: '用户名、姓名和密码不能为空' }, { status: 400 })\n    }\n\n    const trimmedUsername = username.trim()\n    const trimmedName = name.trim()\n    const trimmedPassword = password.trim()\n\n    // 检查用户名是否已存在\n    if (users[trimmedUsername] || Object.values(users).some((u: any) => u.username === trimmedUsername)) {\n      return NextResponse.json({ error: '用户名已存在' }, { status: 400 })\n    }\n\n    // 生成学生ID\n    const studentId = `student_${Date.now()}`\n    \n    // 加密密码\n    const hashedPassword = await bcrypt.hash(trimmedPassword, 10)\n\n    // 创建新学生\n    const newStudent = {\n      id: studentId,\n      username: trimmedUsername,\n      password: hashedPassword,\n      name: trimmedName,\n      role: 'student',\n      teacher: decoded.userId,\n      pages: []\n    }\n\n    // 添加到用户数据\n    users[studentId] = newStudent\n\n    // 保存用户数据\n    fs.writeFileSync(usersPath, JSON.stringify(users, null, 2))\n\n    // 创建学生内容目录\n    const studentContentDir = path.join(process.cwd(), 'data', 'content', studentId)\n    if (!fs.existsSync(studentContentDir)) {\n      fs.mkdirSync(studentContentDir, { recursive: true })\n    }\n\n    // 创建学生页面配置文件\n    const studentPagesPath = path.join(studentContentDir, 'pages.json')\n    const defaultPages = {}\n    fs.writeFileSync(studentPagesPath, JSON.stringify(defaultPages, null, 2))\n\n    return NextResponse.json({ \n      success: true, \n      student: {\n        id: newStudent.id,\n        username: newStudent.username,\n        name: newStudent.name,\n        role: newStudent.role\n      },\n      message: '学生添加成功！'\n    })\n  } catch (error) {\n    console.error('添加学生错误:', error)\n    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAM,GAAG;gBAAE,QAAQ;YAAI;QAC3D;QAEA,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAU,GAAG;gBAAE,QAAQ;YAAI;QAC/D;QAEA,UAAU;QACV,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;QACnD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;QAC7C,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,MAAM,OAAO,KAAK,CAAC,QAAQ,MAAM,CAAC;QAElC,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,WAAW;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,aAAa;QACb,MAAM,WAAW,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,IAC5C,EAAE,IAAI,KAAK,aAAa,EAAE,OAAO,KAAK,QAAQ,MAAM;QAGtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAS;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAU,GAAG;YAAE,QAAQ;QAAI;IAC/D;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAM,GAAG;gBAAE,QAAQ;YAAI;QAC3D;QAEA,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAU,GAAG;gBAAE,QAAQ;YAAI;QAC/D;QAEA,UAAU;QACV,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;QACnD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;QAC7C,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,MAAM,OAAO,KAAK,CAAC,QAAQ,MAAM,CAAC;QAElC,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,WAAW;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAa,GAAG;gBAAE,QAAQ;YAAI;QAClE;QAEA,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEvD,OAAO;QACP,IAAI,CAAC,UAAU,UAAU,CAAC,MAAM,UAAU,CAAC,UAAU,QAAQ;YAC3D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAgB,GAAG;gBAAE,QAAQ;YAAI;QACrE;QAEA,MAAM,kBAAkB,SAAS,IAAI;QACrC,MAAM,cAAc,KAAK,IAAI;QAC7B,MAAM,kBAAkB,SAAS,IAAI;QAErC,aAAa;QACb,IAAI,KAAK,CAAC,gBAAgB,IAAI,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC,IAAW,EAAE,QAAQ,KAAK,kBAAkB;YACnG,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAS,GAAG;gBAAE,QAAQ;YAAI;QAC9D;QAEA,SAAS;QACT,MAAM,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI;QAEzC,OAAO;QACP,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,iBAAiB;QAE1D,QAAQ;QACR,MAAM,aAAa;YACjB,IAAI;YACJ,UAAU;YACV,UAAU;YACV,MAAM;YACN,MAAM;YACN,SAAS,QAAQ,MAAM;YACvB,OAAO,EAAE;QACX;QAEA,UAAU;QACV,KAAK,CAAC,UAAU,GAAG;QAEnB,SAAS;QACT,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,WAAW,KAAK,SAAS,CAAC,OAAO,MAAM;QAExD,WAAW;QACX,MAAM,oBAAoB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,WAAW;QACtE,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,oBAAoB;YACrC,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,mBAAmB;gBAAE,WAAW;YAAK;QACpD;QAEA,aAAa;QACb,MAAM,mBAAmB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,mBAAmB;QACtD,MAAM,eAAe,CAAC;QACtB,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,kBAAkB,KAAK,SAAS,CAAC,cAAc,MAAM;QAEtE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;gBACP,IAAI,WAAW,EAAE;gBACjB,UAAU,WAAW,QAAQ;gBAC7B,MAAM,WAAW,IAAI;gBACrB,MAAM,WAAW,IAAI;YACvB;YACA,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAU,GAAG;YAAE,QAAQ;QAAI;IAC/D;AACF", "debugId": null}}]}