{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/app/api/auth/logout/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport async function POST() {\n  const response = NextResponse.json({ success: true })\n  \n  // 清除认证cookie\n  response.cookies.set('auth-token', '', {\n    httpOnly: true,\n    secure: process.env.NODE_ENV === 'production',\n    sameSite: 'strict',\n    maxAge: 0\n  })\n\n  return response\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe;IACpB,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;IAAK;IAEnD,aAAa;IACb,SAAS,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI;QACrC,UAAU;QACV,QAAQ,oDAAyB;QACjC,UAAU;QACV,QAAQ;IACV;IAEA,OAAO;AACT", "debugId": null}}]}