{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken'\nimport bcrypt from 'bcryptjs'\nimport fs from 'fs'\nimport path from 'path'\n\nexport interface User {\n  id: string\n  username: string\n  password: string\n  name: string\n  pages: string[]\n}\n\nexport interface UserData {\n  [key: string]: User\n}\n\n// 获取用户数据\nexport function getUserData(): UserData {\n  const usersPath = path.join(process.cwd(), 'data', 'users.json')\n  const usersData = fs.readFileSync(usersPath, 'utf8')\n  return JSON.parse(usersData)\n}\n\n// 验证用户凭据\nexport async function validateUser(username: string, password: string): Promise<User | null> {\n  const users = getUserData()\n  const user = Object.values(users).find(u => u.username === username)\n\n  if (!user) {\n    return null\n  }\n\n  const isValid = await bcrypt.compare(password, user.password)\n  return isValid ? user : null\n}\n\n// 生成JWT token\nexport function generateToken(userId: string): string {\n  return jwt.sign({ userId }, process.env.JWT_SECRET!, { expiresIn: '7d' })\n}\n\n// 验证JWT token\nexport function verifyToken(token: string): { userId: string } | null {\n  try {\n    return jwt.verify(token, process.env.JWT_SECRET!) as { userId: string }\n  } catch {\n    return null\n  }\n}\n\n// 获取用户页面配置\nexport function getUserPages(userId: string) {\n  try {\n    const pagesPath = path.join(process.cwd(), 'data', 'content', userId, 'pages.json')\n    const pagesData = fs.readFileSync(pagesPath, 'utf8')\n    return JSON.parse(pagesData)\n  } catch {\n    return {}\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;AAeO,SAAS;IACd,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;IACnD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;IAC7C,OAAO,KAAK,KAAK,CAAC;AACpB;AAGO,eAAe,aAAa,QAAgB,EAAE,QAAgB;IACnE,MAAM,QAAQ;IACd,MAAM,OAAO,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IAE3D,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,UAAU,MAAM,iIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ;IAC5D,OAAO,UAAU,OAAO;AAC1B;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,qIAAA,CAAA,UAAG,CAAC,IAAI,CAAC;QAAE;IAAO,GAAG,QAAQ,GAAG,CAAC,UAAU,EAAG;QAAE,WAAW;IAAK;AACzE;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,qIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU;IACjD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,aAAa,MAAc;IACzC,IAAI;QACF,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,WAAW,QAAQ;QACtE,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;QAC7C,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO,CAAC;IACV;AACF", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/Layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Layout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Layout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/Layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Layout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Layout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/StudyNotes.tsx"], "sourcesContent": ["export default function StudyNotes() {\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-6 flex items-center\">\n          <span className=\"material-icons-outlined text-blue-600 mr-3\">school</span>\n          日语学习回顾\n        </h1>\n        \n        <div className=\"bg-blue-50 border-l-4 border-blue-500 p-4 mb-8\">\n          <div className=\"flex items-center\">\n            <span className=\"material-icons-outlined text-blue-600 mr-2\">event_note</span>\n            <p className=\"text-gray-700\">\n              这是一份根据老师在 <strong>水曜日 7:40</strong> 发送的消息整理的学习笔记。\n            </p>\n          </div>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm\">\n            <thead>\n              <tr className=\"bg-gray-50\">\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b\">类别</th>\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b\">原文</th>\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b\">说明与注解</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-blue-500 mr-2\">volume_up</span>\n                    发音\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b font-medium text-blue-800\">簡単</td>\n                <td className=\"px-6 py-4 border-b text-gray-700\">发音重点练习词：kantan (简单)</td>\n              </tr>\n              <tr className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-blue-500 mr-2\">volume_up</span>\n                    发音\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b font-medium text-blue-800\">パートナー</td>\n                <td className=\"px-6 py-4 border-b text-gray-700\">发音重点练习词：pātonā (伙伴、搭档)</td>\n              </tr>\n              <tr className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-green-500 mr-2\">chat_bubble_outline</span>\n                    表现\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b font-medium text-blue-800\">仕入れ先</td>\n                <td className=\"px-6 py-4 border-b text-gray-700\">词汇学习：しいれさき (供应商)</td>\n              </tr>\n              <tr className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-green-500 mr-2\">chat_bubble_outline</span>\n                    表现\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-gray-400 line-through font-medium\">政権交代</span>\n                    <span className=\"text-red-500 font-bold\">→</span>\n                    <span className=\"bg-green-100 text-green-800 px-2 py-1 rounded font-medium\">世代交代</span>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b text-gray-700\">用法辨析：老师提示将\"政权更迭\"联想到\"世代交替\"。</td>\n              </tr>\n              <tr className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-green-500 mr-2\">chat_bubble_outline</span>\n                    表现\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b font-medium text-blue-800\">終身雇用</td>\n                <td className=\"px-6 py-4 border-b text-gray-700\">词汇学习：しゅうしんこよう (终身雇佣制)</td>\n              </tr>\n              <tr className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-green-500 mr-2\">chat_bubble_outline</span>\n                    表现\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"font-medium text-blue-800\">将来性</span>\n                    <span className=\"text-green-600 font-bold\">≒</span>\n                    <span className=\"font-medium text-blue-800\">伸びしろ</span>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 border-b text-gray-700\">近义词辨析：\"未来潜力\" (しょうらいせい) 与 \"成长空间\" (のびしろ) 意思相近。</td>\n              </tr>\n              <tr className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-green-500 mr-2\">chat_bubble_outline</span>\n                    表现\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 font-medium text-blue-800\">地雷を踏む</td>\n                <td className=\"px-6 py-4 text-gray-700\">惯用语：じらいをふむ，字面意思是\"踩到地雷\"，引申为\"触及敏感或危险的话题\"。</td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n        \n        <div className=\"mt-8 text-center bg-yellow-50 p-4 rounded-lg\">\n          <div className=\"flex items-center justify-center\">\n            <span className=\"material-icons-outlined text-yellow-600 mr-2\">forward_to_inbox</span>\n            <p className=\"text-gray-700 italic\">\n              老师的结束语：\"では、また明日の授業楽しみにしております。\" (那么，期待明天的课。)\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAK,WAAU;sCAA6C;;;;;;wBAAa;;;;;;;8BAI5E,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA6C;;;;;;0CAC7D,8OAAC;gCAAE,WAAU;;oCAAgB;kDACjB,8OAAC;kDAAO;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAKzC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;0CACC,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;sDAAmE;;;;;;sDACjF,8OAAC;4CAAG,WAAU;sDAAmE;;;;;;sDACjF,8OAAC;4CAAG,WAAU;sDAAmE;;;;;;;;;;;;;;;;;0CAGrF,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA6C;;;;;;wDAAgB;;;;;;;;;;;;0DAIjF,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA6C;;;;;;wDAAgB;;;;;;;;;;;;0DAIjF,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA8C;;;;;;wDAA0B;;;;;;;;;;;;0DAI5F,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA8C;;;;;;wDAA0B;;;;;;;;;;;;0DAI5F,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAyC;;;;;;sEACzD,8OAAC;4DAAK,WAAU;sEAAyB;;;;;;sEACzC,8OAAC;4DAAK,WAAU;sEAA4D;;;;;;;;;;;;;;;;;0DAGhF,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA8C;;;;;;wDAA0B;;;;;;;;;;;;0DAI5F,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA8C;;;;;;wDAA0B;;;;;;;;;;;;0DAI5F,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,8OAAC;4DAAK,WAAU;sEAA2B;;;;;;sEAC3C,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;;;;;;0DAGhD,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA8C;;;;;;wDAA0B;;;;;;;;;;;;0DAI5F,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAG,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMhD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA+C;;;;;;0CAC/D,8OAAC;gCAAE,WAAU;0CAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/VocabularyTable.tsx"], "sourcesContent": ["export default function VocabularyTable() {\n  const vocabularyData = [\n    { japanese: \"エレベーター\", chinese: \"电梯\" },\n    { japanese: \"エスカレーター\", chinese: \"自动扶梯\" },\n    { japanese: \"つけっぱなし\", chinese: \"（电器等）一直开着，未关闭\" },\n    { japanese: \"できるだけ\", chinese: \"尽可能，尽量\" },\n    { japanese: \"一週間 (いっしゅうかん)\", chinese: \"一星期，一周\" },\n    { japanese: \"わたしたち\", chinese: \"我们\" },\n    { japanese: \"〜なかで\", chinese: \"在...之中\" },\n    { japanese: \"派遣会社 (はけんがいしゃ)\", chinese: \"人才派遣公司\" },\n    { japanese: \"メッセージ\", chinese: \"消息，信息，留言\" },\n    { japanese: \"恥ずかしがるので (はずかしがるので)\", chinese: \"因为（他/她）害羞\" },\n    { japanese: \"合併されちゃって (がっぺいされちゃって)\", chinese: \"（公司等）被合并了（口语形式）\" }\n  ]\n\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-6 flex items-center\">\n          <span className=\"material-icons-outlined text-blue-600 mr-3\">table_view</span>\n          <span className=\"text-blue-600\">日语词汇</span>\n          <span className=\"text-gray-600 ml-2\">对照表</span>\n        </h1>\n        \n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm\">\n            <thead>\n              <tr className=\"bg-gray-50\">\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-red-600 mr-2\">translate</span>\n                    日语\n                  </div>\n                </th>\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b\">\n                  <div className=\"flex items-center\">\n                    <span className=\"material-icons-outlined text-green-600 mr-2\">g_translate</span>\n                    中文含义\n                  </div>\n                </th>\n              </tr>\n            </thead>\n            <tbody>\n              {vocabularyData.map((item, index) => (\n                <tr key={index} className=\"hover:bg-blue-50 transition-colors\">\n                  <td className=\"px-6 py-4 border-b font-medium text-blue-800 text-lg\">\n                    {item.japanese}\n                  </td>\n                  <td className=\"px-6 py-4 border-b text-gray-700\">\n                    {item.chinese}\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n        \n        <div className=\"mt-8 text-center text-sm text-gray-600 bg-gray-50 p-4 rounded-lg\">\n          <p>共收录 {vocabularyData.length} 个词汇</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,iBAAiB;QACrB;YAAE,UAAU;YAAU,SAAS;QAAK;QACpC;YAAE,UAAU;YAAW,SAAS;QAAO;QACvC;YAAE,UAAU;YAAU,SAAS;QAAgB;QAC/C;YAAE,UAAU;YAAS,SAAS;QAAS;QACvC;YAAE,UAAU;YAAiB,SAAS;QAAS;QAC/C;YAAE,UAAU;YAAS,SAAS;QAAK;QACnC;YAAE,UAAU;YAAQ,SAAS;QAAS;QACtC;YAAE,UAAU;YAAkB,SAAS;QAAS;QAChD;YAAE,UAAU;YAAS,SAAS;QAAW;QACzC;YAAE,UAAU;YAAuB,SAAS;QAAY;QACxD;YAAE,UAAU;YAAyB,SAAS;QAAkB;KACjE;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAK,WAAU;sCAA6C;;;;;;sCAC7D,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;sCAChC,8OAAC;4BAAK,WAAU;sCAAqB;;;;;;;;;;;;8BAGvC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;0CACC,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA4C;;;;;;oDAAgB;;;;;;;;;;;;sDAIhF,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA8C;;;;;;oDAAkB;;;;;;;;;;;;;;;;;;;;;;;0CAMxF,8OAAC;0CACE,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAG,WAAU;0DACX,KAAK,QAAQ;;;;;;0DAEhB,8OAAC;gDAAG,WAAU;0DACX,KAAK,OAAO;;;;;;;uCALR;;;;;;;;;;;;;;;;;;;;;8BAajB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;4BAAE;4BAAK,eAAe,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;;;;;AAKxC", "debugId": null}}, {"offset": {"line": 996, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/NewLesson.tsx"], "sourcesContent": ["export default function NewLesson() {\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-6 flex items-center\">\n          <span className=\"material-icons-outlined text-blue-600 mr-3\">auto_stories</span>\n          新课程内容\n        </h1>\n        \n        <div className=\"space-y-6\">\n          {/* 课程介绍 */}\n          <section className=\"bg-blue-50 p-6 rounded-lg\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-3 flex items-center\">\n              <span className=\"material-icons-outlined text-blue-600 mr-2\">info</span>\n              课程介绍\n            </h2>\n            <p className=\"text-gray-700\">\n              这里是新课程的介绍内容。您可以添加任何学习材料，包括文本、表格、练习等。\n            </p>\n          </section>\n\n          {/* 学习要点 */}\n          <section className=\"bg-green-50 p-6 rounded-lg\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-3 flex items-center\">\n              <span className=\"material-icons-outlined text-green-600 mr-2\">lightbulb</span>\n              学习要点\n            </h2>\n            <ul className=\"space-y-2 text-gray-700\">\n              <li className=\"flex items-start\">\n                <span className=\"material-icons-outlined text-green-600 mr-2 mt-0.5 text-sm\">check_circle</span>\n                要点一：基础语法规则\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"material-icons-outlined text-green-600 mr-2 mt-0.5 text-sm\">check_circle</span>\n                要点二：常用词汇\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"material-icons-outlined text-green-600 mr-2 mt-0.5 text-sm\">check_circle</span>\n                要点三：实际应用\n              </li>\n            </ul>\n          </section>\n\n          {/* 练习题 */}\n          <section className=\"bg-yellow-50 p-6 rounded-lg\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-3 flex items-center\">\n              <span className=\"material-icons-outlined text-yellow-600 mr-2\">quiz</span>\n              练习题\n            </h2>\n            <div className=\"space-y-4\">\n              <div className=\"bg-white p-4 rounded border\">\n                <p className=\"font-medium text-gray-900 mb-2\">1. 请翻译以下句子：</p>\n                <p className=\"text-gray-700 mb-2\">こんにちは、元気ですか？</p>\n                <div className=\"text-sm text-gray-600\">\n                  <p>答案：你好，你好吗？</p>\n                </div>\n              </div>\n              \n              <div className=\"bg-white p-4 rounded border\">\n                <p className=\"font-medium text-gray-900 mb-2\">2. 选择正确的读音：</p>\n                <p className=\"text-gray-700 mb-2\">学校</p>\n                <div className=\"space-y-1 text-sm\">\n                  <label className=\"flex items-center\">\n                    <input type=\"radio\" name=\"q2\" className=\"mr-2\" />\n                    がっこう (正确)\n                  </label>\n                  <label className=\"flex items-center\">\n                    <input type=\"radio\" name=\"q2\" className=\"mr-2\" />\n                    がくこう\n                  </label>\n                </div>\n              </div>\n            </div>\n          </section>\n\n          {/* 补充资料 */}\n          <section className=\"bg-purple-50 p-6 rounded-lg\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-3 flex items-center\">\n              <span className=\"material-icons-outlined text-purple-600 mr-2\">library_books</span>\n              补充资料\n            </h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"bg-white p-4 rounded border\">\n                <h3 className=\"font-medium text-gray-900 mb-2\">相关链接</h3>\n                <ul className=\"text-sm text-blue-600 space-y-1\">\n                  <li><a href=\"#\" className=\"hover:underline\">日语学习网站</a></li>\n                  <li><a href=\"#\" className=\"hover:underline\">在线词典</a></li>\n                </ul>\n              </div>\n              <div className=\"bg-white p-4 rounded border\">\n                <h3 className=\"font-medium text-gray-900 mb-2\">推荐书籍</h3>\n                <ul className=\"text-sm text-gray-700 space-y-1\">\n                  <li>• 《标准日本语》</li>\n                  <li>• 《大家的日语》</li>\n                </ul>\n              </div>\n            </div>\n          </section>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAK,WAAU;sCAA6C;;;;;;wBAAmB;;;;;;;8BAIlF,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAA6C;;;;;;wCAAW;;;;;;;8CAG1E,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAM/B,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAA8C;;;;;;wCAAgB;;;;;;;8CAGhF,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6D;;;;;;gDAAmB;;;;;;;sDAGlG,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6D;;;;;;gDAAmB;;;;;;;sDAGlG,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6D;;;;;;gDAAmB;;;;;;;;;;;;;;;;;;;sCAOtG,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAA+C;;;;;;wCAAW;;;;;;;8CAG5E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAiC;;;;;;8DAC9C,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAClC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAE;;;;;;;;;;;;;;;;;sDAIP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAiC;;;;;;8DAC9C,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAClC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEAAM,MAAK;oEAAQ,MAAK;oEAAK,WAAU;;;;;;gEAAS;;;;;;;sEAGnD,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEAAM,MAAK;oEAAQ,MAAK;oEAAK,WAAU;;;;;;gEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS3D,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAA+C;;;;;;wCAAoB;;;;;;;8CAGrF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAkB;;;;;;;;;;;sEAC5C,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAkB;;;;;;;;;;;;;;;;;;;;;;;sDAGhD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB", "debugId": null}}, {"offset": {"line": 1456, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/TodayLesson.tsx"], "sourcesContent": ["interface PronunciationItem {\n  word: string\n  reading: string\n  meaning: string\n  audio?: string\n}\n\ninterface ExpressionItem {\n  expression: string\n  reading: string\n  meaning: string\n  usage: string\n  example: string\n}\n\ninterface LessonContent {\n  pronunciation: {\n    title: string\n    items: PronunciationItem[]\n  }\n  expressions: {\n    title: string\n    items: ExpressionItem[]\n  }\n}\n\ninterface TodayLessonProps {\n  lesson?: {\n    id: string\n    title: string\n    date: string\n    content: LessonContent\n    homework?: string\n    notes?: string\n  }\n}\n\nexport default function TodayLesson({ lesson }: TodayLessonProps) {\n  // 默认示例数据\n  const defaultLesson = {\n    id: \"sample\",\n    title: \"2024年1月15日 - 日常问候\",\n    date: \"2024-01-15\",\n    content: {\n      pronunciation: {\n        title: \"今日の発音\",\n        items: [\n          {\n            word: \"おはようございます\",\n            reading: \"ohayou gozaimasu\",\n            meaning: \"早上好（敬语）\"\n          },\n          {\n            word: \"こんにちは\", \n            reading: \"konnichiwa\",\n            meaning: \"你好（下午用）\"\n          },\n          {\n            word: \"こんばんは\",\n            reading: \"konbanwa\", \n            meaning: \"晚上好\"\n          }\n        ]\n      },\n      expressions: {\n        title: \"今日の表現\",\n        items: [\n          {\n            expression: \"お疲れ様でした\",\n            reading: \"otsukaresama deshita\",\n            meaning: \"辛苦了\",\n            usage: \"工作结束时对同事说\",\n            example: \"今日もお疲れ様でした。\"\n          },\n          {\n            expression: \"よろしくお願いします\",\n            reading: \"yoroshiku onegaishimasu\", \n            meaning: \"请多关照\",\n            usage: \"初次见面或请求帮助时\",\n            example: \"初めまして、よろしくお願いします。\"\n          }\n        ]\n      }\n    },\n    homework: \"练习今天学习的问候语，明天课堂上演示\",\n    notes: \"注意敬语的使用场合和时间\"\n  }\n\n  const currentLesson = lesson || defaultLesson\n\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        {/* 课程标题 */}\n        <div className=\"border-b border-gray-200 pb-6 mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2 flex items-center\">\n            <span className=\"material-icons-outlined text-blue-600 mr-3\">today</span>\n            今日の授業\n          </h1>\n          <p className=\"text-lg text-gray-600\">{currentLesson.title}</p>\n          <p className=\"text-sm text-gray-500 mt-1\">\n            {new Date(currentLesson.date).toLocaleDateString('ja-JP')}\n          </p>\n        </div>\n\n        {/* 发音练习 */}\n        <section className=\"mb-8\">\n          <h2 className=\"text-2xl font-semibold text-gray-900 mb-6 flex items-center\">\n            <span className=\"material-icons-outlined text-red-500 mr-3\">record_voice_over</span>\n            {currentLesson.content.pronunciation.title}\n          </h2>\n          <div className=\"grid gap-4\">\n            {currentLesson.content.pronunciation.items.map((item, index) => (\n              <div key={index} className=\"bg-red-50 border border-red-200 rounded-lg p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-xl font-bold text-red-800 japanese-text mb-2\">\n                      {item.word}\n                    </h3>\n                    <p className=\"text-red-600 font-medium mb-1\">\n                      [{item.reading}]\n                    </p>\n                    <p className=\"text-gray-700\">\n                      {item.meaning}\n                    </p>\n                  </div>\n                  <button className=\"bg-red-600 text-white p-3 rounded-full hover:bg-red-700 transition-colors\">\n                    <span className=\"material-icons-outlined\">volume_up</span>\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* 表达练习 */}\n        <section className=\"mb-8\">\n          <h2 className=\"text-2xl font-semibold text-gray-900 mb-6 flex items-center\">\n            <span className=\"material-icons-outlined text-green-500 mr-3\">chat_bubble</span>\n            {currentLesson.content.expressions.title}\n          </h2>\n          <div className=\"grid gap-6\">\n            {currentLesson.content.expressions.items.map((item, index) => (\n              <div key={index} className=\"bg-green-50 border border-green-200 rounded-lg p-6\">\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4\">\n                  <div>\n                    <h3 className=\"text-xl font-bold text-green-800 japanese-text mb-2\">\n                      {item.expression}\n                    </h3>\n                    <p className=\"text-green-600 font-medium mb-2\">\n                      [{item.reading}]\n                    </p>\n                    <p className=\"text-gray-700 mb-3\">\n                      <strong>意思：</strong>{item.meaning}\n                    </p>\n                    <p className=\"text-gray-600 text-sm\">\n                      <strong>使用场合：</strong>{item.usage}\n                    </p>\n                  </div>\n                  <div className=\"bg-white p-4 rounded border border-green-300\">\n                    <h4 className=\"font-medium text-gray-900 mb-2 flex items-center\">\n                      <span className=\"material-icons-outlined text-green-600 mr-2 text-sm\">format_quote</span>\n                      例句\n                    </h4>\n                    <p className=\"japanese-text text-green-800 font-medium\">\n                      {item.example}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* 作业和备注 */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {currentLesson.homework && (\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-3 flex items-center\">\n                <span className=\"material-icons-outlined text-yellow-600 mr-2\">assignment</span>\n                宿題\n              </h3>\n              <p className=\"text-gray-700\">{currentLesson.homework}</p>\n            </div>\n          )}\n          \n          {currentLesson.notes && (\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-3 flex items-center\">\n                <span className=\"material-icons-outlined text-blue-600 mr-2\">note</span>\n                注意事項\n              </h3>\n              <p className=\"text-gray-700\">{currentLesson.notes}</p>\n            </div>\n          )}\n        </div>\n\n        {/* 学习进度 */}\n        <div className=\"mt-8 bg-gray-50 rounded-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n            <span className=\"material-icons-outlined text-purple-600 mr-2\">trending_up</span>\n            学習進捗\n          </h3>\n          <div className=\"flex items-center space-x-4\">\n            <button className=\"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center\">\n              <span className=\"material-icons-outlined mr-2\">check_circle</span>\n              完了\n            </button>\n            <button className=\"bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 flex items-center\">\n              <span className=\"material-icons-outlined mr-2\">bookmark</span>\n              保存\n            </button>\n            <button className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center\">\n              <span className=\"material-icons-outlined mr-2\">quiz</span>\n              練習\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAqCe,SAAS,YAAY,EAAE,MAAM,EAAoB;IAC9D,SAAS;IACT,MAAM,gBAAgB;QACpB,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;YACP,eAAe;gBACb,OAAO;gBACP,OAAO;oBACL;wBACE,MAAM;wBACN,SAAS;wBACT,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;wBACT,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;wBACT,SAAS;oBACX;iBACD;YACH;YACA,aAAa;gBACX,OAAO;gBACP,OAAO;oBACL;wBACE,YAAY;wBACZ,SAAS;wBACT,SAAS;wBACT,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,YAAY;wBACZ,SAAS;wBACT,SAAS;wBACT,OAAO;wBACP,SAAS;oBACX;iBACD;YACH;QACF;QACA,UAAU;QACV,OAAO;IACT;IAEA,MAAM,gBAAgB,UAAU;IAEhC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAA6C;;;;;;gCAAY;;;;;;;sCAG3E,8OAAC;4BAAE,WAAU;sCAAyB,cAAc,KAAK;;;;;;sCACzD,8OAAC;4BAAE,WAAU;sCACV,IAAI,KAAK,cAAc,IAAI,EAAE,kBAAkB,CAAC;;;;;;;;;;;;8BAKrD,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAA4C;;;;;;gCAC3D,cAAc,OAAO,CAAC,aAAa,CAAC,KAAK;;;;;;;sCAE5C,8OAAC;4BAAI,WAAU;sCACZ,cAAc,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACpD,8OAAC;oCAAgB,WAAU;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,KAAK,IAAI;;;;;;kEAEZ,8OAAC;wDAAE,WAAU;;4DAAgC;4DACzC,KAAK,OAAO;4DAAC;;;;;;;kEAEjB,8OAAC;wDAAE,WAAU;kEACV,KAAK,OAAO;;;;;;;;;;;;0DAGjB,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;;;;;;;mCAdtC;;;;;;;;;;;;;;;;8BAuBhB,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAA8C;;;;;;gCAC7D,cAAc,OAAO,CAAC,WAAW,CAAC,KAAK;;;;;;;sCAE1C,8OAAC;4BAAI,WAAU;sCACZ,cAAc,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAClD,8OAAC;oCAAgB,WAAU;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,KAAK,UAAU;;;;;;kEAElB,8OAAC;wDAAE,WAAU;;4DAAkC;4DAC3C,KAAK,OAAO;4DAAC;;;;;;;kEAEjB,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;0EAAO;;;;;;4DAAa,KAAK,OAAO;;;;;;;kEAEnC,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;0EAAO;;;;;;4DAAe,KAAK,KAAK;;;;;;;;;;;;;0DAGrC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;0EAAsD;;;;;;4DAAmB;;;;;;;kEAG3F,8OAAC;wDAAE,WAAU;kEACV,KAAK,OAAO;;;;;;;;;;;;;;;;;;mCAtBX;;;;;;;;;;;;;;;;8BAgChB,8OAAC;oBAAI,WAAU;;wBACZ,cAAc,QAAQ,kBACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAA+C;;;;;;wCAAiB;;;;;;;8CAGlF,8OAAC;oCAAE,WAAU;8CAAiB,cAAc,QAAQ;;;;;;;;;;;;wBAIvD,cAAc,KAAK,kBAClB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAA6C;;;;;;wCAAW;;;;;;;8CAG1E,8OAAC;oCAAE,WAAU;8CAAiB,cAAc,KAAK;;;;;;;;;;;;;;;;;;8BAMvD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAA+C;;;;;;gCAAkB;;;;;;;sCAGnF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;wCAAmB;;;;;;;8CAGpE,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;wCAAe;;;;;;;8CAGhE,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;wCAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxE", "debugId": null}}, {"offset": {"line": 1996, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/app/%5Bstudent%5D/%5Bpage%5D/page.tsx"], "sourcesContent": ["import { getUserPages } from '@/lib/auth'\nimport Layout from '@/components/Layout'\nimport StudyNotes from '@/components/StudyNotes'\nimport VocabularyTable from '@/components/VocabularyTable'\nimport NewLesson from '@/components/NewLesson'\nimport TodayLesson from '@/components/TodayLesson'\nimport { notFound } from 'next/navigation'\nimport fs from 'fs'\nimport path from 'path'\n\ninterface PageProps {\n  params: Promise<{ student: string; page: string }>\n}\n\nexport default async function DynamicPage({ params }: PageProps) {\n  const { student, page } = await params\n  const pages = getUserPages(student)\n\n  // 检查页面是否存在\n  if (!pages[page]) {\n    notFound()\n  }\n\n  const pageInfo = pages[page]\n\n  // 获取课程数据（如果是课程页面）\n  const getLessonData = () => {\n    if (pageInfo.type === 'lesson' && pageInfo.lessonId) {\n      try {\n        const lessonsPath = path.join(process.cwd(), 'data', 'lessons.json')\n        if (fs.existsSync(lessonsPath)) {\n          const lessonsData = fs.readFileSync(lessonsPath, 'utf8')\n          const { lessons } = JSON.parse(lessonsData)\n          return lessons.find((lesson: any) => lesson.id === pageInfo.lessonId)\n        }\n      } catch (error) {\n        console.error('获取课程数据失败:', error)\n      }\n    }\n    return null\n  }\n\n  const lessonData = getLessonData()\n\n  // 根据页面类型渲染不同的内容\n  const renderContent = () => {\n    switch (page) {\n      case 'cet6':\n        return <StudyNotes />\n      case 'jp':\n        return <VocabularyTable />\n      case 'new-lesson':\n        return <NewLesson />\n      case 'vocabulary':\n        return (\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"bg-white rounded-lg shadow-lg p-8\">\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-6 flex items-center\">\n                <span className=\"material-icons-outlined text-blue-600 mr-3\">quiz</span>\n                词汇练习\n              </h1>\n              <div className=\"text-center py-12\">\n                <span className=\"material-icons-outlined text-gray-400 text-6xl mb-4\">\n                  construction\n                </span>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  功能开发中\n                </h3>\n                <p className=\"text-gray-600\">\n                  词汇练习功能正在开发中，敬请期待！\n                </p>\n              </div>\n            </div>\n          </div>\n        )\n      case 'grammar':\n        return (\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"bg-white rounded-lg shadow-lg p-8\">\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-6 flex items-center\">\n                <span className=\"material-icons-outlined text-blue-600 mr-3\">auto_stories</span>\n                日语语法学习\n              </h1>\n              <div className=\"space-y-6\">\n                <div className=\"bg-blue-50 p-6 rounded-lg\">\n                  <h2 className=\"text-xl font-semibold text-gray-900 mb-3\">基础语法</h2>\n                  <p className=\"text-gray-700\">\n                    这里将包含日语基础语法规则和例句。\n                  </p>\n                </div>\n                <div className=\"bg-green-50 p-6 rounded-lg\">\n                  <h2 className=\"text-xl font-semibold text-gray-900 mb-3\">进阶语法</h2>\n                  <p className=\"text-gray-700\">\n                    这里将包含日语进阶语法内容。\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )\n      case 'reading':\n        return (\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"bg-white rounded-lg shadow-lg p-8\">\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-6 flex items-center\">\n                <span className=\"material-icons-outlined text-blue-600 mr-3\">menu_book</span>\n                阅读理解\n              </h1>\n              <div className=\"space-y-6\">\n                <div className=\"bg-yellow-50 p-6 rounded-lg\">\n                  <h2 className=\"text-xl font-semibold text-gray-900 mb-3\">阅读材料</h2>\n                  <p className=\"text-gray-700\">\n                    这里将包含各种日语阅读材料和练习。\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )\n      default:\n        // 检查是否为课程页面\n        if (pageInfo.type === 'lesson') {\n          return <TodayLesson lesson={lessonData} />\n        }\n\n        return (\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"bg-white rounded-lg shadow-lg p-8\">\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">\n                {pageInfo.title}\n              </h1>\n              <p className=\"text-gray-600\">\n                {pageInfo.description}\n              </p>\n            </div>\n          </div>\n        )\n    }\n  }\n\n  return (\n    <Layout studentId={student} currentPage={page} pages={pages}>\n      {renderContent()}\n    </Layout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;;;;AAMe,eAAe,YAAY,EAAE,MAAM,EAAa;IAC7D,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,MAAM;IAChC,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD,EAAE;IAE3B,WAAW;IACX,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;QAChB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,WAAW,KAAK,CAAC,KAAK;IAE5B,kBAAkB;IAClB,MAAM,gBAAgB;QACpB,IAAI,SAAS,IAAI,KAAK,YAAY,SAAS,QAAQ,EAAE;YACnD,IAAI;gBACF,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;gBACrD,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,cAAc;oBAC9B,MAAM,cAAc,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,aAAa;oBACjD,MAAM,EAAE,OAAO,EAAE,GAAG,KAAK,KAAK,CAAC;oBAC/B,OAAO,QAAQ,IAAI,CAAC,CAAC,SAAgB,OAAO,EAAE,KAAK,SAAS,QAAQ;gBACtE;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;YAC7B;QACF;QACA,OAAO;IACT;IAEA,MAAM,aAAa;IAEnB,gBAAgB;IAChB,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,gIAAA,CAAA,UAAU;;;;;YACpB,KAAK;gBACH,qBAAO,8OAAC,qIAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,+HAAA,CAAA,UAAS;;;;;YACnB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAA6C;;;;;;oCAAW;;;;;;;0CAG1E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAsD;;;;;;kDAGtE,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;YAOvC,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAA6C;;;;;;oCAAmB;;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAI/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzC,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAA6C;;;;;;oCAAgB;;;;;;;0CAG/E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzC;gBACE,YAAY;gBACZ,IAAI,SAAS,IAAI,KAAK,UAAU;oBAC9B,qBAAO,8OAAC,iIAAA,CAAA,UAAW;wBAAC,QAAQ;;;;;;gBAC9B;gBAEA,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,SAAS,KAAK;;;;;;0CAEjB,8OAAC;gCAAE,WAAU;0CACV,SAAS,WAAW;;;;;;;;;;;;;;;;;QAKjC;IACF;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAM;QAAC,WAAW;QAAS,aAAa;QAAM,OAAO;kBACnD;;;;;;AAGP", "debugId": null}}]}