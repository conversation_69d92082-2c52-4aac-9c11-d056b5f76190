import Link from 'next/link'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <span className="material-icons-outlined text-gray-400 text-8xl">
            error_outline
          </span>
        </div>
        
        <h1 className="text-3xl font-bold text-gray-900 mb-4">页面不存在</h1>
        <p className="text-gray-600 mb-8">
          抱歉，您访问的页面不存在或您没有权限访问。
        </p>
        
        <div className="space-y-4">
          <Link
            href="/login"
            className="block w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            返回登录页
          </Link>
          <button
            onClick={() => window.history.back()}
            className="block w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors"
          >
            返回上一页
          </button>
        </div>
      </div>
    </div>
  )
}
