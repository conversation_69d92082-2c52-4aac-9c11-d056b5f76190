import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'
import fs from 'fs'
import path from 'path'

export interface User {
  id: string
  username: string
  password: string
  name: string
  role?: string
  teacher?: string
  students?: string[]
  pages?: string[]
}

export interface UserData {
  [key: string]: User
}

// 获取用户数据
export function getUserData(): UserData {
  const usersPath = path.join(process.cwd(), 'data', 'users.json')
  const usersData = fs.readFileSync(usersPath, 'utf8')
  return JSON.parse(usersData)
}

// 验证用户凭据
export async function validateUser(username: string, password: string): Promise<User | null> {
  const users = getUserData()
  const user = Object.values(users).find(u => u.username === username)

  if (!user) {
    return null
  }

  const isValid = await bcrypt.compare(password, user.password)
  return isValid ? user : null
}

// 生成JWT token
export function generateToken(userId: string): string {
  return jwt.sign({ userId }, process.env.JWT_SECRET!, { expiresIn: '7d' })
}

// 验证JWT token
export function verifyToken(token: string): { userId: string } | null {
  try {
    return jwt.verify(token, process.env.JWT_SECRET!) as { userId: string }
  } catch {
    return null
  }
}

// 获取用户页面配置
export function getUserPages(userId: string) {
  try {
    const pagesPath = path.join(process.cwd(), 'data', 'content', userId, 'pages.json')
    const pagesData = fs.readFileSync(pagesPath, 'utf8')
    return JSON.parse(pagesData)
  } catch {
    return {}
  }
}
