import Link from 'next/link'

interface LogoProps {
  userRole?: string
  userId?: string
}

export default function Logo({ userRole, userId }: LogoProps) {
  const getHomeUrl = () => {
    if (userRole === 'teacher') {
      return '/teacher'
    } else if (userRole === 'student' && userId) {
      return `/${userId}`
    }
    return '/login'
  }

  return (
    <Link 
      href={getHomeUrl()}
      className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
    >
      <div className="bg-blue-600 text-white p-2 rounded-lg">
        <span className="material-icons-outlined text-xl">school</span>
      </div>
      <div className="hidden md:block">
        <h1 className="text-lg font-bold text-gray-900">日语学习网站</h1>
        <p className="text-xs text-gray-600">Japanese Learning Platform</p>
      </div>
    </Link>
  )
}
