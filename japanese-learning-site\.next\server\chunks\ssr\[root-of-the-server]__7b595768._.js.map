{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken'\nimport bcrypt from 'bcryptjs'\nimport fs from 'fs'\nimport path from 'path'\n\nexport interface User {\n  id: string\n  username: string\n  password: string\n  name: string\n  role?: string\n  teacher?: string\n  students?: string[]\n  pages?: string[]\n}\n\nexport interface UserData {\n  [key: string]: User\n}\n\n// 获取用户数据\nexport function getUserData(): UserData {\n  const usersPath = path.join(process.cwd(), 'data', 'users.json')\n  const usersData = fs.readFileSync(usersPath, 'utf8')\n  return JSON.parse(usersData)\n}\n\n// 验证用户凭据\nexport async function validateUser(username: string, password: string): Promise<User | null> {\n  const users = getUserData()\n  const user = Object.values(users).find(u => u.username === username)\n\n  if (!user) {\n    return null\n  }\n\n  const isValid = await bcrypt.compare(password, user.password)\n  return isValid ? user : null\n}\n\n// 生成JWT token\nexport function generateToken(userId: string): string {\n  return jwt.sign({ userId }, process.env.JWT_SECRET!, { expiresIn: '7d' })\n}\n\n// 验证JWT token\nexport function verifyToken(token: string): { userId: string } | null {\n  try {\n    return jwt.verify(token, process.env.JWT_SECRET!) as { userId: string }\n  } catch {\n    return null\n  }\n}\n\n// 获取用户页面配置\nexport function getUserPages(userId: string) {\n  try {\n    const pagesPath = path.join(process.cwd(), 'data', 'content', userId, 'pages.json')\n    const pagesData = fs.readFileSync(pagesPath, 'utf8')\n    return JSON.parse(pagesData)\n  } catch {\n    return {}\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;AAkBO,SAAS;IACd,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;IACnD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;IAC7C,OAAO,KAAK,KAAK,CAAC;AACpB;AAGO,eAAe,aAAa,QAAgB,EAAE,QAAgB;IACnE,MAAM,QAAQ;IACd,MAAM,OAAO,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IAE3D,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,UAAU,MAAM,iIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ;IAC5D,OAAO,UAAU,OAAO;AAC1B;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,qIAAA,CAAA,UAAG,CAAC,IAAI,CAAC;QAAE;IAAO,GAAG,QAAQ,GAAG,CAAC,UAAU,EAAG;QAAE,WAAW;IAAK;AACzE;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,qIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU;IACjD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,aAAa,MAAc;IACzC,IAAI;QACF,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,WAAW,QAAQ;QACtE,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;QAC7C,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO,CAAC;IACV;AACF", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/Layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Layout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Layout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/Layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Layout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Layout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/StudentLessons.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StudentLessons.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StudentLessons.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/components/StudentLessons.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StudentLessons.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StudentLessons.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/program/website/japanese-learning-site/src/app/%5Bstudent%5D/page.tsx"], "sourcesContent": ["import { getUserPages } from '@/lib/auth'\nimport Layout from '@/components/Layout'\nimport StudentLessons from '@/components/StudentLessons'\nimport Link from 'next/link'\n\ninterface StudentPageProps {\n  params: Promise<{ student: string }>\n}\n\nexport default async function StudentPage({ params }: StudentPageProps) {\n  const { student } = await params\n  const pages = getUserPages(student)\n\n  return (\n    <Layout studentId={student} pages={pages}>\n      <div className=\"space-y-8\">\n        {/* 课程列表 */}\n        <StudentLessons studentId={student} />\n\n        {/* 其他学习资源 */}\n        {Object.keys(pages).length > 0 && (\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"bg-white rounded-lg shadow-lg p-8\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-6 flex items-center\">\n                <span className=\"material-icons-outlined text-green-600 mr-3\">library_books</span>\n                其他学习资源\n              </h2>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {Object.entries(pages).map(([pageId, pageInfo]) => (\n                  <Link\n                    key={pageId}\n                    href={`/${student}/${pageId}`}\n                    className=\"block bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg p-6 hover:shadow-lg transition-all duration-200 hover:scale-105\"\n                  >\n                    <div className=\"flex items-center mb-4\">\n                      <span className=\"material-icons-outlined text-green-600 text-3xl mr-3\">\n                        {pageInfo.icon}\n                      </span>\n                      <h3 className=\"text-xl font-semibold text-gray-900\">\n                        {pageInfo.title}\n                      </h3>\n                    </div>\n                    <p className=\"text-gray-600 mb-4\">\n                      {pageInfo.description}\n                    </p>\n                    <div className=\"flex items-center text-green-600 font-medium\">\n                      <span>开始学习</span>\n                      <span className=\"material-icons-outlined ml-1\">arrow_forward</span>\n                    </div>\n                  </Link>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </Layout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAMe,eAAe,YAAY,EAAE,MAAM,EAAoB;IACpE,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM;IAC1B,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD,EAAE;IAE3B,qBACE,8OAAC,4HAAA,CAAA,UAAM;QAAC,WAAW;QAAS,OAAO;kBACjC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,oIAAA,CAAA,UAAc;oBAAC,WAAW;;;;;;gBAG1B,OAAO,IAAI,CAAC,OAAO,MAAM,GAAG,mBAC3B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAA8C;;;;;;oCAAoB;;;;;;;0CAIpF,8OAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,QAAQ,SAAS,iBAC5C,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ;wCAC7B,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,SAAS,IAAI;;;;;;kEAEhB,8OAAC;wDAAG,WAAU;kEACX,SAAS,KAAK;;;;;;;;;;;;0DAGnB,8OAAC;gDAAE,WAAU;0DACV,SAAS,WAAW;;;;;;0DAEvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;;;uCAjB5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BzB", "debugId": null}}]}