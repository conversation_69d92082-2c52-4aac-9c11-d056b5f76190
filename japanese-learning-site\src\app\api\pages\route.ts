import { NextRequest, NextResponse } from 'next/server'
import { verifyToken, getUserPages } from '@/lib/auth'
import fs from 'fs'
import path from 'path'

// 获取用户页面列表
export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value
    
    if (!token) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }

    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: '无效token' }, { status: 401 })
    }

    const pages = getUserPages(decoded.userId)
    return NextResponse.json({ pages })
  } catch (error) {
    console.error('获取页面列表错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// 添加新页面
export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value
    
    if (!token) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }

    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: '无效token' }, { status: 401 })
    }

    const { pageId, title, description, icon, type } = await request.json()

    if (!pageId || !title) {
      return NextResponse.json({ error: '页面ID和标题不能为空' }, { status: 400 })
    }

    // 读取现有页面配置
    const pagesPath = path.join(process.cwd(), 'data', 'content', decoded.userId, 'pages.json')
    let pages = {}
    
    try {
      const pagesData = fs.readFileSync(pagesPath, 'utf8')
      pages = JSON.parse(pagesData)
    } catch {
      // 如果文件不存在，创建目录
      const dir = path.dirname(pagesPath)
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }
    }

    // 添加新页面
    pages[pageId] = {
      title,
      description: description || '',
      icon: icon || 'article',
      type: type || 'custom'
    }

    // 保存更新后的配置
    fs.writeFileSync(pagesPath, JSON.stringify(pages, null, 2))

    return NextResponse.json({ success: true, pages })
  } catch (error) {
    console.error('添加页面错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// 删除页面
export async function DELETE(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value
    
    if (!token) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }

    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: '无效token' }, { status: 401 })
    }

    const { pageId } = await request.json()

    if (!pageId) {
      return NextResponse.json({ error: '页面ID不能为空' }, { status: 400 })
    }

    // 读取现有页面配置
    const pagesPath = path.join(process.cwd(), 'data', 'content', decoded.userId, 'pages.json')
    
    if (!fs.existsSync(pagesPath)) {
      return NextResponse.json({ error: '页面配置文件不存在' }, { status: 404 })
    }

    const pagesData = fs.readFileSync(pagesPath, 'utf8')
    const pages = JSON.parse(pagesData)

    if (!pages[pageId]) {
      return NextResponse.json({ error: '页面不存在' }, { status: 404 })
    }

    // 删除页面
    delete pages[pageId]

    // 保存更新后的配置
    fs.writeFileSync(pagesPath, JSON.stringify(pages, null, 2))

    return NextResponse.json({ success: true, pages })
  } catch (error) {
    console.error('删除页面错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
